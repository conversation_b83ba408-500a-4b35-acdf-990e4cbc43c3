import type { Contains } from 'definition/common';

import * as React from 'react';
import styled from 'styled-components';

export const StyleTable = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

export const StyleThead = styled.tr`
  background-color: #f5f5f5;
  line-height: 40px;
  padding: 0 10px;
  color: #323233;
  th {
    font-weight: bold;
    text-align: center;

    i {
      font-weight: normal;
    }

    // &:nth-child(2) {
    //   text-align: left;
    // }
    &:last-child {
      text-align: right;
      padding-right: 12px;
    }
  }
`;

export const EmptyTd = styled.td`
  height: 10px;
`;

export const EmptyLine = (): JSX.Element => (
  <tr>
    <EmptyTd />
  </tr>
);

export const StyleSelector = styled.div`
  display: flex;
  align-items: center;
  position: ${(props: Contains<{ position: string }>): string => props.position || 'static'};
  bottom: 15px;
  margin-bottom: 15px;
  background-color: #fff;
  .zent-checkbox-wrap {
    margin-right: 5px;
  }
`;

export const PaginationWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
`;
