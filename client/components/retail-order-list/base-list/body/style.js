import styled from 'styled-components';

const BaseTr = styled.tr`
  border: ${({ needBorder }) => (needBorder ? '1px solid #e5e5e5' : 'none')};
  td {
    padding: 0 12px;
  }
`;

export const StyleItemHeader = styled(BaseTr)`
  background-color: #f7f7f7;
  border-bottom: none;
  line-height: 40px;
`;

export const StyleItemBody = styled.tr`
  border: 1px solid #e5e5e5;
  td {
    text-align: center;
    height: 80px;
    border-right: 1px solid #e5e5e5;
    vertical-align: middle;
    // &:first-child {
    //   text-align: left;
    // }
  }
`;

export const StyleItemFooter = styled(BaseTr)`
  border-top: none;
  td {
    padding: 0;
  }
`;
