import React from 'react';
import PropTypes from 'prop-types';
import { get } from 'lodash';
import OrderItem from './item';

class Body extends React.Component {
  static propTypes = {
    columns: PropTypes.array,
    renderHeader: PropTypes.func,
    renderFooter: PropTypes.func,
    datasets: PropTypes.array,
    rowKey: PropTypes.string,
    pageInfo: PropTypes.object,
    onPageChange: PropTypes.func
  };

  static defaultProps = {
    columns: [],
    datasets: [],
    pageInfo: {}
  };

  getBody = () => {
    const { datasets, rowKey, columns, renderFooter, renderHeader } = this.props;

    return datasets.map((data, idx) => {
      const itemProps = {
        key: rowKey ? get(data, rowKey) : idx,
        data,
        columns,
        renderFooter,
        renderHeader
      };

      return <OrderItem {...itemProps} />;
    });
  };

  render() {
    return <tbody>{this.getBody()}</tbody>;
  }
}

export default Body;
