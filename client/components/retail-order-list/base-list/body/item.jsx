import React, { Fragment } from 'react';
import { isFunction, get } from 'lodash';
import PropTypes from 'prop-types';
import { StyleItemFooter, StyleItemBody, StyleItemHeader } from './style';
import { EmptyLine } from '../style';

function Header({ renderHeader, columnsLength, data }) {
  return (
    <StyleItemHeader needBorder={!!renderHeader}>
      <td colSpan={columnsLength}>{renderHeader && renderHeader(data)}</td>
    </StyleItemHeader>
  );
}

class Footer extends React.Component {
  state = {
    withBorder: false
  };

  // a little ugly
  checkContentEmpty = () => {
    const { withBorder: prevWithBorder } = this.state;
    const withBorder = !!this.content.innerHTML;
    if (prevWithBorder !== withBorder) {
      this.setState({
        withBorder
      });
    }
  };

  componentDidMount() {
    !!this.props.renderFooter && this.checkContentEmpty();
  }

  render() {
    const { renderFooter, columnsLength, data } = this.props;
    const { withBorder } = this.state;
    return (
      <Fragment>
        {renderFooter && (
          <StyleItemFooter needBorder={withBorder}>
            <td
              colSpan={columnsLength}
              ref={content => {
                this.content = content;
              }}
            >
              {renderFooter(data)}
            </td>
          </StyleItemFooter>
        )}
        <EmptyLine />
      </Fragment>
    );
  }
}

const Body = ({ columns, data }) => (
  <StyleItemBody>
    {columns.map(({ bodyRender, name, label, formatter, colSpan }, idx) => {
      if (colSpan === 0) {
        return null;
      }

      if (isFunction(bodyRender)) {
        return (
          // 下面两个用到 index 的地方, 不涉及元素删减, 是可以使用的
          // eslint-disable-next-line react/no-array-index-key
          <td key={idx} colSpan={colSpan}>
            {bodyRender(data, label)}
          </td>
        );
      }

      const content = get(data, name, null);
      return (
        // eslint-disable-next-line react/no-array-index-key
        <td key={idx} colSpan={colSpan}>
          {formatter ? formatter(content) : content}
        </td>
      );
    })}
  </StyleItemBody>
);

function OrderItem(props) {
  const { renderFooter, renderHeader, data, columns } = props;

  const columnsLength = columns.length;
  const headerProps = {
    renderHeader,
    columnsLength,
    data
  };

  const footerProps = {
    renderFooter,
    columnsLength,
    data
  };
  return (
    <Fragment>
      <Header {...headerProps} />
      <Body columns={columns} data={data} />
      <Footer {...footerProps} />
    </Fragment>
  );
}

OrderItem.propTypes = {
  columns: PropTypes.array,
  renderHeader: PropTypes.func,
  renderFooter: PropTypes.func,
  data: PropTypes.object
};

export default OrderItem;
