import React, { Fragment } from 'react';
import PropTypes from 'prop-types';
import { pick, isFunction } from 'lodash';
import { BlockLoading, Pagination, Checkbox } from 'zent';

import Header from './header';
import Body from './body';
import { StyleTable, StyleSelector, PaginationWrapper } from './style';

const throughBodyProps = [
  'rowKey',
  'columns',
  'pageInfo',
  'onPageChange',
  'datasets',
  'renderHeader',
  'renderFooter',
  'initFetch'
];

class OrderList extends React.Component {
  static propTypes = {
    columns: PropTypes.array,
    className: PropTypes.string,
    renderHeader: PropTypes.func,
    renderFooter: PropTypes.func,
    loading: PropTypes.bool,
    datasets: PropTypes.array,
    rowKey: PropTypes.string,
    pageInfo: PropTypes.object,
    onPageChange: PropTypes.func,
    emptyContent: PropTypes.node,
    initFetch: PropTypes.func
  };

  batchComponents = ({ batchComponents, batchComponentRenderer }) => {
    const { initFetch } = this.props;
    if (isFunction(batchComponentRenderer)) {
      return batchComponentRenderer(initFetch);
    }
    if (batchComponents) {
      return <div>{batchComponents}</div>;
    }
    return null;
  };

  allSelector = () => {
    const { selection, handleAllSelector } = this.props.selection;
    if (selection) {
      const { batchComponentsAutoFixed = true } = selection;
      const { handleCheck, checked } = handleAllSelector();
      return (
        <StyleSelector position={batchComponentsAutoFixed && 'sticky'}>
          <Checkbox onChange={handleCheck} checked={checked}>
            当页全选
          </Checkbox>
          {this.batchComponents({ ...selection })}
        </StyleSelector>
      );
    }
    return null;
  };

  render() {
    const { columns, loading, pageInfo, emptyContent, datasets, onPageChange } = this.props;

    if (loading) {
      return <BlockLoading loading />;
    }
    if (!datasets.length) {
      return emptyContent || null;
    }
    const bodyProps = pick(this.props, throughBodyProps);
    return (
      <Fragment>
        <StyleTable>
          <Header columns={columns} />
          <Body {...bodyProps} />
        </StyleTable>
        {this.allSelector()}
        <PaginationWrapper>
          <Pagination
            onChange={({ current }) => {
              onPageChange({ pageNo: current });
            }}
            pageSize={pageInfo.pageSize}
            current={pageInfo.pageNo || pageInfo.current}
            total={pageInfo.totalItem || pageInfo.total}
          />
        </PaginationWrapper>
      </Fragment>
    );
  }
}

export default OrderList;
