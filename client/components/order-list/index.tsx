import * as React from 'react';
import { Pagination, BlockLoading } from 'zent';
import { get, omit, isEqual } from 'lodash';
import { global } from '@youzan/retail-utils';

import EmptyBody from 'components/table-empty-body';

import { IOrderList } from 'definition/order-list';
import { useLocalStorage } from '@youzan/react-hooks';
import ListHeader from './list-header';
import OrderItem from './order-item';
import { OrderInfoContext, ExternalChannelErrorContext } from './context';

const PaginationWrapperStyle: React.CSSProperties = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  flexDirection: 'row-reverse',
  flexWrap: 'wrap-reverse'
};

const {
  KDT_ID,
  USER_INFO: { adminId: USER_ID }
} = global;

const EXTERNAL_CHANNEL_ERROR_KEY = `${KDT_ID}_${USER_ID}_EXTERNAL_CHANNEL_ERROR`;

const Empty = ({ loading }: { loading: boolean }): React.ReactElement =>
  loading ? <BlockLoading loading /> : <EmptyBody text="暂无订单" />;

const OrderList = React.memo(
  (props: IOrderList) => {
    const {
      listData,
      config,
      loading,
      pageInfo,
      itemCpm,
      onItemCheckBoxChange,
      selectHelper: SelectHelper,
      showEmptySelectHelper,
      checkedOrderNos = [],
      reload,
      orderSourceParam,
      cloudColumns
    } = props;
    const Item = itemCpm || OrderItem;
    const { total, pageNo, onChange, pageSize, current, totalItem } = pageInfo;
    const [canShowExternalChannelError, setCanShowExternalChannelError] = useLocalStorage(
      EXTERNAL_CHANNEL_ERROR_KEY,
      true
    );

    return listData.length > 0 ? (
      <BlockLoading loading={loading}>
        <ListHeader config={config} cloudColumns={cloudColumns} />
        <ExternalChannelErrorContext.Provider
          value={{
            canShow: canShowExternalChannelError,
            onCanShowChange: flag => setCanShowExternalChannelError(flag)
          }}
        >
          {listData.map((orderInfo, index) => {
            const key = config.isFulfillOrder
              ? get(orderInfo, 'fulfillOrder.fulfillNo', index)
              : get(orderInfo, 'mainOrderInfo.orderNo', index);
            const { orderAddressInfo, fulfillOrder } = orderInfo;

            return (
              <OrderInfoContext.Provider value={orderInfo} key={key}>
                <Item
                  data={orderInfo}
                  config={config}
                  cloudColumns={cloudColumns}
                  checked={(checkedOrderNos as any).includes(key)}
                  onItemCheckBoxChange={() => onItemCheckBoxChange && onItemCheckBoxChange(key)}
                  reload={reload}
                  orderSourceParam={orderSourceParam}
                  orderAddressInfo={orderAddressInfo}
                  paymentInfo={orderInfo.paymentInfo}
                  fulfillOrder={fulfillOrder}
                />
              </OrderInfoContext.Provider>
            );
          })}
        </ExternalChannelErrorContext.Provider>

        <footer style={PaginationWrapperStyle}>
          <Pagination
            current={pageNo || current}
            total={total || totalItem}
            onChange={onChange}
            pageSize={pageSize}
          />
          {/* 这一部分逻辑在批量发货中用到, 其他组件不需要 */}
          {SelectHelper ? <SelectHelper /> : null}
        </footer>
      </BlockLoading>
    ) : (
      <>
        <Empty loading={loading} />
        {showEmptySelectHelper && (SelectHelper ? <SelectHelper /> : null)}
      </>
    );
  },
  (preProps, nextProps) => {
    const preCompareProps: Record<string, any> = omit(preProps, 'pageInfo');
    const nextCompareProps: Record<string, any> = omit(nextProps, 'pageInfo');
    const prePropsKeys = Object.keys(preCompareProps);
    const nextPropsKeys = Object.keys(nextCompareProps);
    const equal =
      prePropsKeys.length === nextPropsKeys.length &&
      prePropsKeys.every(key => preCompareProps[key] === nextCompareProps[key]);
    return isEqual(preProps.pageInfo, nextProps.pageInfo) && equal;
  }
);

export default OrderList;
