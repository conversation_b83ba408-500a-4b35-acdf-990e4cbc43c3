import { isBranchStore, isHqStore, isPartnerStore } from '@youzan/utils-shop';

// GoodsList中会新增cell的配置项
export const GOODS_LIST_CELL_OPTIONS = ['withExpressStatus', 'withRefundInfo'];

// 网店发货配置
export const shipmentsConfig = {
  goodsList: {
    withExpressStatus: true
  },
  itemHeader: {
    withSelfFetchAddr: false,
    withExpress: true,
    withExpectDeliveryTime: true
  },
  packInfo: true,
  receiverInfo: true,
  operation: true,
  itemFooter: {
    withDeliveryInfo: true
  }
};

// 连锁版网店发货配置(履约单列表)
export const chainShipmentsConfig = {
  isFulfillOrder: true,
  goodsList: true,
  itemHeader: {
    withExpectDeliveryTime: true,
    withExpressStore: isHqStore || isPartnerStore,
    withOrderType: true
  },
  receiverInfo: true,
  operation: true,
  fulfillStatus: true,
  expressType: true,
  itemFooter: {
    withDeliveryInfo: true
  }
};

// 单店版到店自提列表
export const fetchListConfig = {
  goodsList: {
    withExpressStatus: true
  },
  itemHeader: {
    withSelfFetchAddr: true
  },
  packInfo: false,
  receiverInfo: true,
  operation: true
};

// 连锁版到店自提列表
export const chainFetchListConfig = {
  goodsList: {
    withExpressStatus: true
  },
  isFulfillOrder: true,
  itemHeader: {
    withSelfFetchAddr: true
  },
  packInfo: false,
  receiverInfo: true,
  operation: true
};

export const queryListConfig = {
  goodsList: {
    withPrice: true,
    withRefundInfo: true
  },
  itemHeader: {
    withSaleWay: true,
    withStar: true,
    withExtraInfo: true,
    withMore: true,
    withOversold: true
  },
  receiverInfo: {
    withBuyer: true
  },
  expressType: true,
  orderState: true,
  realpay: true,
  operation: true,
  itemFooter: {
    withDeliveryInfo: true
  }
};

// 连锁版查询列表配置
export const chainQueryListConfig = {
  goodsList: {
    withPrice: true,
    withRefundInfo: true
  },
  itemHeader: {
    // 总有总店显示销售渠道
    withSaleWay: isHqStore || isPartnerStore || isBranchStore,
    withStar: true,
    withExtraInfo: true,
    withMore: true
  },
  receiverInfo: {
    withBuyer: true
  },
  expressType: true,
  orderState: true,
  realpay: true,
  operation: true,
  itemFooter: {
    withDeliveryInfo: true
  }
};

/**
 * 批量发货的配置
 * 显示配送方式
 * 显示收获地址
 * 显示当前快递状态
 *
 * 头部需要快递信息
 * 不需要额外的操作(备注之类的)
 * 需要发货复选框
 */
export const batchExpressListConfig = {
  packInfo: true,
  receiveAddress: true,
  receiverInfo: {
    withBuyer: false,
    widthAddressDetail: true
  },
  goodsList: {
    withExpressStatus: true,
    withRefunStatusTxt: true // 只针对电子面单
  },
  itemHeader: {
    withExpress: true,
    noOption: true,
    needDispatchCheckbox: true
  }
};
