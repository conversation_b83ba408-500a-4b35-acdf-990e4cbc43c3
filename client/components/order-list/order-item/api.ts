import { request } from '@youzan/retail-utils';

/**
 * 获取物流包裹信息
 * data: express_id, express_no
 */
export function fetchLogisticsInfo(data: { express_id: string; express_no: string }) {
  return request({
    url: `${window._global.url.www}/trade/order/expressInfo.json`,
    data
  });
}

/**
 * 订单加星
 */
export function addStar(data: { order_no?: string; star: number }) {
  return request({
    method: 'post',
    url: '/youzan.retail.trade.seller.order/1.0.0/star',
    data
  });
}
