@import '~shared/style';

.risk-content {
  width: 400px;
}

.oversold-pop {
  max-width: 320px;
}

.oversold-icon {
  margin-right: 10px;
}

.header-basic-info {
  .oversold-icon {
    height: 21px;
    line-height: 21px;
    padding: 0 4px;
    white-space: nowrap;
  }

  .risk-tag {
    height: 21px;
    line-height: 21px !important;
    padding: 0 4px !important;
  }
}

:local(.order-item) {
  margin-bottom: 10px;

  .opreations {
    margin-right: 10px;
    display: inline-block;

    &:last-child {
      margin-right: 0;
    }
  }

  .order-item__deliver-info {
    color: $color-text-secondary;
    background-color: $color-white;
    border: 1px solid $border-color-base;
    border-top: none;
    padding: 0 10px;

    .localdeliver-info-item {
      height: 40px;
      line-height: 40px;
      border-bottom: 1px solid $border-color-base;
      display: flex;
      justify-content: space-between;

      .item-text-warning {
        color: $color-alert;
      }

      .item-text-okay {
        color: $color-success;
      }

      .svg-motorbike {
        margin-right: 10px;
      }

      .options {
        margin-left: 10px;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .order-item__header {
    .basic-info {
      display: flex;
      justify-content: space-between;
      min-width: 790px;
      background-color: $background-color-base;
      border: 1px solid $border-color-base;
      padding: 12px 10px;

      .info-wrapper {
        display: flex;
        flex-wrap: wrap;

        .risk {
          margin-right: 5px;

          &-tag {
            color: $color-white;
            background-color: $color-warn;
            font-size: $font-size-small;
            padding: 2px 4px;
            line-height: 1;
            border-radius: 2px;
            cursor: pointer;
          }
        }

        .basic {
          display: flex;
          align-items: center;
          margin-right: 15px;

          .zent-checkbox-wrap {
            vertical-align: top;
          }

          @media screen and (max-width: 1400px) {
            margin-right: 8px;
          }

          &.link-blue-color {
            color: $color-link;
            cursor: pointer;
          }

          .saleway-with-dot {
            display: inline-block;
            font-size: $font-size-xlarge;
            line-height: 21px;
            vertical-align: top;
            margin-right: 5px;

            &.online {
              color: $color-alert;
            }

            &.offline {
              color: $color-link;
            }
          }

          .zent-popover-wrapper {
            display: inline-flex !important;
          }

          .ellipsisText {
            display: inline-block;
          }
        }

        .supplier-name {
          display: flex;
        }

        .express-way {
          color: $color-text-primary;
        }

        .color-orange {
          color: $color-warn;
        }
      }

      .align-right {
        white-space: nowrap;

        .remark {
          color: $color-link;
          cursor: pointer;
        }
      }

      .light-grey {
        color: $color-text-disable;
      }
    }

    .header-basic-info {
      .info-wrapper {
        flex-wrap: nowrap;
        .basic {
          white-space: nowrap;
          align-items: flex-start;
        }

        .basic-wrap {
          display: inline-flex;
          flex-wrap: wrap;
        }
      }
    }
  }

  .order-item__body {
    display: flex;
    border: 1px solid $border-color-base;
    border-top: none;

    .cell {
      border-right: 1px solid $border-color-base;
      flex: 1;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 60px;

      &.left {
        padding: 0 10px;
        border: none;
        align-items: flex-start;
        text-align: left;
      }

      .gray {
        color: $color-text-secondary;
      }

      &--goods-list {
        display: flex;
        flex-direction: column;
        padding: 0;
        align-items: flex-start;

        .goods-item {
          display: flex;
          flex: 1;
          width: 100%;
          box-sizing: border-box;
          border-bottom: 1px solid $color-secondary-button-active;

          &:first-of-type {
            border-top: none;
          }

          &:last-of-type {
            border-bottom: 0;
          }

          .goods-info {
            flex: 1;
            flex-basis: 65%;
            padding: 10px 12px;
          }

          &__cell {
            display: flex;
            padding: 10px;
            flex: 1;
            align-items: center;

            &.express-status {
              flex-basis: 20%;
              justify-content: center;
            }

            .express-wrapper {
              text-align: center;
            }

            .refund-status {
              display: block;
              color: $color-warn;
            }

            &.refund-info {
              border-left: 1px solid $border-color-base;
              flex-direction: column;
              justify-content: center;
              min-width: 60px;
              flex-basis: 60px;

              .opt-item {
                margin-bottom: 8px;
              }

              .orange-button {
                border: 1px solid $color-warn;
                color: $color-warn;
                border-radius: 3px;
                height: 28px;
                line-height: 18px;
                padding: 3px;
                white-space: nowrap;
              }
            }
          }

          .express-finished {
            color: $color-success;
          }

          .goods-remark,
          .produce-shop,
          .goods-status,
          .goods-num {
            flex-direction: column;
            justify-content: center;
            padding: 5px;
            flex-basis: 20%;

            > p:last-child {
              margin-top: 10px;
            }

            > p:first-child {
              margin-top: 0;
            }
          }

          .goods-remark {
            flex-basis: 20%;
            word-break: break-all;
          }

          .produce-shop {
            flex-basis: 20%;
          }

          .goods-status {
            flex-basis: 24%;
            text-align: center;
          }
        }
      }

      &--pack-info {
        min-width: 200px;
        flex-direction: column;
        padding: 0;

        .pack-item {
          flex: 1;
          border-top: 1px solid $border-color-base;
          width: 100%;
          display: flex;
          align-items: center;

          &:first-of-type {
            border-top: none;
          }

          &__content {
            padding: 10px;
            line-height: 15px;
          }
        }
      }

      &--receiver-info {
        text-align: center;
        flex-direction: column;
      }

      &--realpay {
        flex-direction: column;
        white-space: nowrap;

        .buyway {
          white-space: normal;
        }
      }

      &--opts {
        border: none;
        display: flex;
        flex-direction: column;
        padding-right: 12px;
        align-items: flex-end;

        &__item {
          text-align: center;
          margin-bottom: 5px;
          white-space: nowrap;
        }

        .zent-btn {
          margin-left: 0;
        }

        .refunding {
          color: $color-warn;
        }
      }
    }
  }

  .order-item__footer {
    border: 1px solid $border-color-base;
    border-top: 0;
    padding: 4px 8px;
    color: $color-text-secondary;

    &.delivery-info {
      line-height: 50px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;

      .delivery-info__text {
        color: $color-success;

        .svg-motorbike {
          margin-right: 5px;
        }
      }

      .delivery-info__operations {
        .zent-btn {
          margin-right: 10px;
        }
      }
    }

    &.remark-info {
      .remark-info__memo {
        color: $color-warn;
        word-break: break-all;
      }
    }

    &.process-operation-error-info {
      height: 37px;
      line-height: 37px;
      font-size: $font-size-small;
      color: $color-warn;

      .zenticon-warning {
        margin: 0 5px;
      }
    }

    &.channel-error-info {
      height: 37px;
      display: flex;
      align-items: center;
      font-size: $font-size-small;
      color: $color-warn;
      .zenticon-warning {
        margin: 0 5px;
      }
      .zenticon-close {
        margin-left: auto;
        cursor: pointer;
        color: $color-text-disable;
      }
    }
  }
}

.tips-pop {
  &__content {
    padding: 5px 0;
    min-width: 230px;
  }

  .content__label {
    color: $color-text-secondary;
    padding: 10px 0 15px;
  }

  .content__actions {
    padding-top: 15px;
    display: flex;
    justify-content: flex-end;
  }
}
