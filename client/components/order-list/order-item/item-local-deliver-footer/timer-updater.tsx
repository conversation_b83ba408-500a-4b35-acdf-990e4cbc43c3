import * as React from 'react';

import { ITimerUpdateRenderProps } from './interfaces';

interface ITimerUpdater {
  callback: (state: ITimerUpdateRenderProps) => void;
  render: (state: ITimerUpdateRenderProps) => string;
}

class TimerUpdater extends React.Component<ITimerUpdater> {
  timer: number;

  state = {
    now: +new Date()
  };

  componentDidMount() {
    this.timer = (setInterval(() => {
      this.setState({ now: new Date() });
    }, 1000) as unknown) as number;
  }

  componentDidUpdate() {
    const { callback } = this.props;
    callback && callback(this.state);
  }

  componentWillUnmount() {
    clearInterval(this.timer);
  }

  render() {
    return this.props.render(this.state);
  }
}

export default TimerUpdater;
