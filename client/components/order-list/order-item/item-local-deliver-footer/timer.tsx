import * as React from 'react';
import { get } from 'lodash';
import { format } from 'date-fns';

class Timer extends React.Component<{ deliveryItem: string }> {
  state = {
    now: new Date()
  };

  timer: number;

  renderCallTime = (data: string) => {
    // const startTime = new Date(get(data, 'takeoutExpressDetail.createTime'));
    const startTime = new Date(
      format(get(data, 'takeoutExpressDetail.createTime'), 'YYYY/MM/DD HH:mm:ss')
    );
    let calledTime: number = +this.state.now;
    calledTime = Number.parseInt(`${(+calledTime - +startTime) / 1000}`, 10);
    calledTime = Math.max(+calledTime, 0);
    const rst = `${Number.parseInt(`${calledTime / 60}`, 10)}分${calledTime % 60}秒`;
    return rst;
  };

  componentDidMount() {
    this.timer = (setInterval(() => {
      this.setState({ now: new Date() });
    }, 1000) as unknown) as number;
  }

  componentWillUnmount() {
    clearInterval(this.timer);
  }

  render() {
    const { deliveryItem } = this.props;

    // 没有相关字段，直接不展示（2020-09-07 餐道项目）
    if (!deliveryItem || !get(deliveryItem, 'takeoutExpressDetail.createTime')) return null;

    return (
      <span>
        已呼叫时长：
        {this.renderCallTime(this.props.deliveryItem)}
      </span>
    );
  }
}

export default Timer;
