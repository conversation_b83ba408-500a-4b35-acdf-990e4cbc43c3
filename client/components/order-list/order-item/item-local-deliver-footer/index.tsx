import * as React from 'react';
import { get, filter, isEmpty } from 'lodash';
import { IOrderListItemFooterConfig } from 'definition/order-list-config';
import {
  IOrderExpressInfo,
  IMainOrderInfo,
  IOrderAddressInfo,
  IItemInfo,
  IOperations,
  IOperation,
  IAutoCallInfo
} from 'definition/order-info';
import { DeepPartial } from 'utility-types';

import {
  ADD_TIPS,
  DELIVERY_CANCEL,
  DELIVERY_RECALL,
  DELIVERY_VIEW_DETAILS
} from 'components/opt-components/constant';

import { OptComponent } from '../../../opt-components';
import { DeliveryInfo, DeliveryInfoStatus } from '../components/delivery-info';

import AutoCallText from './autocall-text';
import Timer from './timer';

function filterByCode(operations: IOperations = [], codes: IOperation['code'][] = []) {
  return operations.filter(opt => codes.indexOf(opt.code) > -1);
}

export interface ItemLocalDeliverFooterProps extends IOrderListItemFooterConfig {
  reload: () => void;
  isFulfillOrder: boolean;
  orderExpressInfo: IOrderExpressInfo;
  itemInfo: IItemInfo;
  mainOrderInfo: IMainOrderInfo;
  orderAddressInfo: IOrderAddressInfo;
  deliveryNo?: string;
}

export default class ItemLocalDeliverFooter extends React.Component<ItemLocalDeliverFooterProps> {
  state = {
    outOfDate: false
  };

  static defaultProps: DeepPartial<ItemLocalDeliverFooterProps> = {
    orderExpressInfo: {},
    mainOrderInfo: {},
    itemInfo: [],
    orderAddressInfo: {}
  };

  renderAutoCall() {
    const {
      orderExpressInfo: { autoCallInfo = {} as IAutoCallInfo },
      isFulfillOrder,
      deliveryNo
    } = this.props;
    const operations = get(autoCallInfo, 'operations', []);

    if (isEmpty(autoCallInfo)) return null;

    return (
      <div className="order-item__deliver-info">
        <DeliveryInfo
          status={DeliveryInfoStatus.Success}
          renderOperations={
            !this.state.outOfDate
              ? () => {
                  return operations.map((opt: IOperation) => (
                    <OptComponent
                      operation={opt}
                      options={{
                        reload: this.props.reload,
                        orderNo: this.props.mainOrderInfo.orderNo,
                        deliveryNo: isFulfillOrder ? deliveryNo : null,
                        callback: this.props.reload
                      }}
                    />
                  ));
                }
              : undefined
          }
        >
          <AutoCallText
            autoCallInfo={autoCallInfo}
            outOfDate={this.state.outOfDate}
            onOutOfDate={() => {
              this.setState({ outOfDate: true });
            }}
          />
        </DeliveryInfo>
      </div>
    );
  }

  render() {
    const {
      orderExpressInfo: { packs = [], autoCallInfo = {} },
      mainOrderInfo: { orderNo, storeId },
      itemInfo,
      orderAddressInfo
    } = this.props;

    if ((autoCallInfo as IOrderExpressInfo['autoCallInfo']).isShow) return this.renderAutoCall();

    // 第三方配送信息
    const deliveryInfo = packs.reduce((pre, packInfo) => {
      const {
        takeoutExpressDetail,
        operations,
        itemIds,
        packId,
        warehouseId,
        expressType,
        sendType
      } = packInfo;
      return isEmpty(takeoutExpressDetail)
        ? pre
        : [
            ...pre,
            {
              ...takeoutExpressDetail,
              operations,
              itemIds,
              packId,
              warehouseId,
              expressType,
              fullPackInfo: packInfo,
              takeoutExpressDetail,
              sendType
            }
          ];
    }, []);

    const displayDelivery = filter(
      deliveryInfo,
      item =>
        // 商家自行配送不显示 底部
        item.sendType !== 22
    );

    if (!(this.props.withDeliveryInfo && displayDelivery.length > 0)) return null;

    return (
      <div className="order-item__deliver-info">
        {displayDelivery.map(deliveryItem => {
          const okay = (deliveryItem as any).statusCode < 5;
          return (
            <DeliveryInfo
              status={okay ? DeliveryInfoStatus.Success : DeliveryInfoStatus.Error}
              renderTextOperations={() => {
                return filterByCode(deliveryItem.operations, [DELIVERY_VIEW_DETAILS]).map(opt => (
                  <OptComponent
                    // eslint-disable-next-line react/jsx-props-no-spreading
                    {...this.props}
                    key={opt.code}
                    operation={opt}
                    options={{
                      reload: this.props.reload,
                      orderNo,
                      packInfo: deliveryItem,
                      itemInfo,
                      orderAddressInfo,
                      storeId: storeId || 0,
                      callback: this.props.reload
                    }}
                  />
                ));
              }}
              renderOperations={() => {
                return [
                  (deliveryItem as any).statusCode === 1 && <Timer deliveryItem={deliveryItem} />,
                  filterByCode(deliveryItem.operations, [
                    DELIVERY_CANCEL,
                    ADD_TIPS,
                    DELIVERY_RECALL
                  ]).map(opt => (
                    <OptComponent
                      key={opt.code}
                      operation={opt}
                      options={{
                        reload: this.props.reload,
                        orderNo,
                        packInfo: deliveryItem,
                        itemInfo,
                        orderAddressInfo,
                        storeId: storeId || 0,
                        callback: this.props.reload
                      }}
                    />
                  ))
                ];
              }}
            >
              {(deliveryItem as any).statusInfo || '无信息'}
            </DeliveryInfo>
          );
        })}
      </div>
    );
  }
}
