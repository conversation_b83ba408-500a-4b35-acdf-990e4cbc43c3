import * as React from 'react';
import { formatDate } from '@youzan/retail-utils';

import isNil from 'lodash/isNil';
import { IAutoCallInfo } from 'definition/order-info';
import { SupportParallelCallInLocalDelivery } from 'common/constant';
import TimerUpdater from './timer-updater';
import { ITimerUpdateRenderProps } from './interfaces';

export interface IAutoCallText {
  autoCallInfo: IAutoCallInfo;
  outOfDate: boolean;
  onOutOfDate: () => void;
}

export default class AutoCallText extends React.Component<IAutoCallText> {
  callback = ({ now }: ITimerUpdateRenderProps) => {
    const {
      autoCallInfo: { autoDeliveryTime },
      outOfDate,
      onOutOfDate
    } = this.props;
    const autoCallDate = isNil(autoDeliveryTime) ? 0 : +new Date(autoDeliveryTime);
    if (!outOfDate && autoCallDate - now <= 0) {
      onOutOfDate?.();
    }
  };

  render() {
    const {
      autoCallInfo: { autoDeliveryTime, autoDeliveryDesc }
    } = this.props;
    const autoCallDate = +new Date(autoDeliveryTime ?? 0);

    return (
      <TimerUpdater
        callback={this.callback}
        render={({ now }: ITimerUpdateRenderProps) => {
          if (SupportParallelCallInLocalDelivery) {
            return autoDeliveryDesc ?? '';
          }

          if (autoCallDate - now > 0) {
            return `已开启自动呼叫，系统将在 ${formatDate(
              autoCallDate,
              'YYYY-MM-DD HH:mm'
            )} 自动呼叫配送员`;
          }
          return '准备呼叫配送员状态中，请稍等';
        }}
      />
    );
  }
}
