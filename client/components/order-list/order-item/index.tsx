import * as React from 'react';
import { ExpressType } from '@youzan/zan-hasaki';

import { IOrderList } from 'definition/order-list';
import { IMainOrderInfo } from 'definition/order-info';
import ItemHeader from './item-header';
import ItemFooter from './item-footer';
import ItemBody from './item-body';
import ItemWrapper from './item-wrapper';
import ItemLocalDeliverFooter from './item-local-deliver-footer';
import ItemExpressDeliverFooter from './item-express-delivery-footer';

type IOrderItemProps = Pick<
  IOrderList,
  | 'data'
  | 'config'
  | 'reload'
  | 'onItemCheckBoxChange'
  | 'checked'
  | 'orderSourceParam'
  | 'orderAddressInfo'
  | 'paymentInfo'
  | 'fulfillOrder'
  | 'cloudColumns'
>;

interface IRemarkInfo {
  sellerStar: number;
  buyerMemoDesc: string;
  sellerMemoDesc: string;
  deliveryMemo?: string;
  sellerRemarkPics?: string[];
}

interface IState {
  remarkInfo: IRemarkInfo;
  founderToMemberMark: string;
}

class OrderItem extends React.Component<IOrderItemProps, IState> {
  state = {
    remarkInfo: {
      ...this.setRemarkInfo(this.props.data.remarkInfo),
      deliveryMemo: this.props.data?.fulfillOrder?.deliveryMemo ?? '' // 订单列表没有这个数据, 所以要防御一下
    },
    founderToMemberMark: this.setFounderToMemberRemark(this.props.data.mainOrderInfo)
  };

  UNSAFE_componentWillReceiveProps(nextProps: IOrderItemProps) {
    this.setState(prevState => {
      return {
        remarkInfo: {
          ...this.setRemarkInfo(nextProps.data.remarkInfo),
          deliveryMemo: prevState.remarkInfo.deliveryMemo
        }
      };
    });
  }

  setRemarkInfo(remarkInfo: IRemarkInfo) {
    return {
      sellerStar: remarkInfo.sellerStar ?? 0,
      buyerMemoDesc: remarkInfo.buyerMemoDesc ?? '',
      sellerMemoDesc: remarkInfo.sellerMemoDesc ?? '',
      remarkPics: remarkInfo.sellerRemarkPics ?? []
    };
  }

  setFounderToMemberRemark(mainOrderInfo: IMainOrderInfo) {
    try {
      const { extraInfo = '{}' } = mainOrderInfo;
      const { ATTR_QTT_ORDER_EXPORT = '{}' } = JSON.parse(extraInfo);
      const { founderToMemberMark = '' } = JSON.parse(ATTR_QTT_ORDER_EXPORT);
      return founderToMemberMark;
      // eslint-disable-next-line no-empty
    } catch (error) {
      return '';
    }
  }

  //  备注信息修改
  handleRemarkChange = (remark: string, remarkPics: string[]) => {
    const { remarkInfo } = this.state;
    const key = this.props.config.isFulfillOrder ? 'deliveryMemo' : 'sellerMemoDesc';
    remarkInfo[key] = remark;
    remarkInfo.remarkPics = remarkPics;
    this.setState({ remarkInfo });
  };

  // 团长备注信息修改
  handleFounderRemarkChange = (remark: string) => {
    this.setState({ founderToMemberMark: remark });
  };

  // 加星
  handleStarChange = (star: number) => {
    const { remarkInfo } = this.state;
    remarkInfo.sellerStar = star;
    this.setState({ remarkInfo });
  };

  render() {
    const {
      data,
      config,
      reload,
      onItemCheckBoxChange,
      checked,
      orderSourceParam,
      orderAddressInfo,
      paymentInfo,
      fulfillOrder,
      cloudColumns
    } = this.props;
    const expressType = this.props?.data?.mainOrderInfo?.expressType;
    // expressType === 同城配送 只在同城配送 下显示 配送详情 底部栏
    const showLocalFooter = expressType === ExpressType.City;
    const showExpressFooter = expressType === ExpressType.Express;
    return (
      <ItemWrapper>
        <ItemHeader
          // 下面这个 props spreading 数据来源跟其他的区分度很大, 好找, 所以保留
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...config.itemHeader}
          checked={checked}
          isFulfillOrder={config.isFulfillOrder}
          remark={
            config.isFulfillOrder
              ? this.state.remarkInfo.deliveryMemo
              : this.state.remarkInfo.sellerMemoDesc
          }
          remarkPics={this.state.remarkInfo.remarkPics}
          onRemarkChange={this.handleRemarkChange}
          onFounderRemarkChange={this.handleFounderRemarkChange}
          onItemCheckBoxChange={onItemCheckBoxChange}
          star={this.state.remarkInfo.sellerStar || 0}
          onStarChange={this.handleStarChange}
          orderSourceParam={orderSourceParam}
          mainOrderInfo={data.mainOrderInfo}
          sourceInfo={data.sourceInfo}
          orderAddressInfo={orderAddressInfo}
          paymentInfo={paymentInfo}
          supplierInfo={data.goodsSupplierInfo}
          fulfillOrder={fulfillOrder}
          exchangeInfo={data.exchangeInfo}
          operations={data.operations}
          founderToMemberMark={this.state.founderToMemberMark}
        />
        <ItemBody
          data={data}
          config={config}
          cloudColumns={cloudColumns}
          reload={reload as () => void}
        />
        <ItemFooter
          encryptStr={data.encryptStr}
          remarkInfo={this.state.remarkInfo}
          buyerInvoiceInfo={data.buyerInvoiceInfo}
          includeMockItem={data.includeMockItem}
          itemInfo={data.itemInfo}
          mainOrderInfo={data.mainOrderInfo}
          config={config}
          founderToMemberMark={this.state.founderToMemberMark}
        />
        {showLocalFooter && (
          <ItemLocalDeliverFooter
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...config.itemFooter}
            // 下面这个实在是改不动了, 因为 ...this.props 传给了 OptComponent, 后面的操作比较难看懂
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...data}
            isFulfillOrder={config.isFulfillOrder}
            reload={reload}
          />
        )}
        {showExpressFooter && (
          <ItemExpressDeliverFooter
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...config.itemFooter}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...data}
            isFulfillOrder={config.isFulfillOrder}
            reload={reload}
          />
        )}
      </ItemWrapper>
    );
  }
}

export default OrderItem;
