import * as React from 'react';
import { IOrderListItemFooterConfig } from 'definition/order-list-config';
import {
  IOrderExpressInfo,
  IMainOrderInfo,
  IOrderAddressInfo,
  IItemInfo
} from 'definition/order-info';
import { LOGISTICS_DETAILS, LOGISTICS_REMARK } from 'components/opt-components/constant';
import { OptComponent } from '../../../opt-components';
import { DeliveryInfo, DeliveryInfoStatus } from '../components/delivery-info';

export interface ItemExpressDeliveryFooterProps extends IOrderListItemFooterConfig {
  reload: () => void;
  isFulfillOrder: boolean;
  orderExpressInfo: IOrderExpressInfo;
  itemInfo: IItemInfo;
  mainOrderInfo: IMainOrderInfo;
  orderAddressInfo: IOrderAddressInfo;
  deliveryNo?: string;
}

export default function ItemExpressDeliveryFooter(props: ItemExpressDeliveryFooterProps) {
  const {
    orderExpressInfo: { packs = [] },
    mainOrderInfo,
    orderAddressInfo
  } = props;
  const { orderNo } = mainOrderInfo;
  const filteredPacks = packs.filter(pack => {
    return (
      pack.expressDetail &&
      pack.expressDetail.tansitInfo &&
      pack.expressDetail.tansitInfo.abnormalLogisticsInfo
    );
  });

  if (filteredPacks.length === 0) {
    return null;
  }

  return (
    <div className="order-item__deliver-info">
      {filteredPacks.map(pack => {
        return (
          <DeliveryInfo
            status={DeliveryInfoStatus.Error}
            renderTextOperations={() => {
              return pack.operations
                .filter(opt => opt.code === LOGISTICS_DETAILS)
                .map(opt => {
                  return (
                    <OptComponent
                      {...props}
                      key={opt.code}
                      operation={opt}
                      options={{
                        mainOrderInfo,
                        orderNo,
                        packInfo: pack,
                        orderAddressInfo,
                        reload: props.reload
                      }}
                    />
                  );
                });
            }}
            renderOperations={() => {
              return pack.operations
                .filter(opt => opt.code === LOGISTICS_REMARK)
                .map(opt => {
                  return (
                    <OptComponent
                      {...props}
                      key={opt.code}
                      operation={opt}
                      options={{
                        mainOrderInfo,
                        orderNo,
                        packInfo: pack,
                        orderAddressInfo,
                        reload: props.reload
                      }}
                    />
                  );
                });
            }}
          >
            {pack.expressDetail.tansitInfo.abnormalLogisticsInfo}
          </DeliveryInfo>
        );
      })}
    </div>
  );
}
