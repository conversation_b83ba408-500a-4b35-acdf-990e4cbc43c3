import * as React from 'react';
import cx from 'classnames';
import { Tag, Notify, Pop, Checkbox } from 'zent';
import { isEmpty, isNil, get } from 'lodash';
import { deepJsonParse, formatDatetime, setUrlDomain } from '@youzan/retail-utils';
import * as global from '@youzan/utils-shop';
import { BlankLink, PopEllipsisText } from '@youzan/react-components';
import { ChannelType, ExpressType, OrderStateType, hasLiteAbility } from 'common/constants/common';
import { OrderNoTitleRecord, ORDER_TYPE } from 'common/constants/order';
import { OptComponent } from 'components/opt-components';
import StarOpener from 'components/star';
import { addStar } from 'components/order-list/order-item/api';
import { returnByGlobal } from 'common/utils';
import { notOptRemarkAndStar } from 'common/constant';
import {
  isRetailSingleStore,
  isUnifiedShop,
  isFrontWarehouse,
  isUnifiedOfflineBranchStore
} from '@youzan/utils-shop';
import { IMainOrderInfo, ISelfFetchInfo } from 'definition/order-info';
import { IItemHeader } from 'definition/order-list';
import { DeepPartial } from 'utility-types';
import { IError } from 'definition/common';
import ModifyReceiverInfo from 'components/details-cpn/order-info/modify-receiver-info';
import SupplierName from 'components/supplier-name';
import { checkAccess } from '@youzan/sam-components';
import BasicWrap from './basic-wrap';

interface IRenderPopText {
  count: number;
  text?: string;
  cpn?: React.ReactElement;
}

interface Icons {
  code: string;
  priority: null;
  text: string;
  type: string;
}

/**
 * 订单基本信息  以及一些操作
 * props: {
 *  mainOrderInfo,
 *  orderAddressInfo
 * }
 * @class ItemHeader
 * @extends {Component}
 */
class ItemHeader extends React.Component<IItemHeader> {
  static defaultProps: DeepPartial<IItemHeader> = {
    remark: '',
    star: 0,
    mainOrderInfo: {},
    remarkInfo: {},
    orderAddressInfo: {},
    paymentInfo: {},
    fulfillOrder: {},
    sourceInfo: { orderSource: undefined },
    operations: [],
    exchangeInfo: {},
    founderToMemberMark: false
  };

  _hasShopName: boolean;

  _shopName?: string;

  // 是否是离线订单
  isOfflineOrder = (mainOrderInfo: IMainOrderInfo) => {
    const tagsInfo = deepJsonParse(mainOrderInfo.tagsInfo);
    return !isEmpty(tagsInfo) && tagsInfo.IS_OFFLINE_ORDER;
  };

  // 备注信息change
  handleRemarkChange = (remark: string, remarkPics: string[]) => {
    const { onRemarkChange } = this.props;
    onRemarkChange && onRemarkChange(remark, remarkPics);
  };

  // 团长备注信息change
  handleFounderRemarkChange = (remark: string) => {
    const { onFounderRemarkChange } = this.props;
    onFounderRemarkChange && onFounderRemarkChange(remark);
  };

  // 加星修改
  handleStarChange = (star: number) => {
    const { orderNo } = this.props.mainOrderInfo;
    addStar({
      order_no: orderNo,
      star
    })
      .then(() => {
        Notify.success('加星成功！');
        this.props.onStarChange && this.props.onStarChange(star);
      })
      .catch((err: IError) => {
        Notify.error(err.msg || '加星失败！');
      });
  };

  getShopName() {
    const { saleWayDesc, saleWayShopName, shopName } = this.props.mainOrderInfo;
    if (this._hasShopName) {
      return this._shopName;
    }
    this._shopName = returnByGlobal(
      {
        isRetailSingleStore: saleWayDesc,
        isSepHqStore: shopName,
        isPartnerStore: shopName,
        isRetailMinimalistShop: shopName,
        isSepBranchStore: saleWayDesc,
        isUnitedHqStore: saleWayShopName,
        isUnitedBranchStore: undefined,
        isPHShop: saleWayDesc
      },
      global
    );
    this._hasShopName = true;
    return this._shopName;
  }

  // 渲染销售渠道(带一个圆圈)
  renderSaleWay = ({ count, text }: IRenderPopText) => {
    const {
      mainOrderInfo: { saleWay }
    } = this.props;

    const saleWayClassName = ChannelType.Online === saleWay ? 'online' : 'offline';
    return (
      <span className="basic">
        {this.renderPopText({
          count,
          text,
          cpn: <span className={cx('saleway-with-dot', saleWayClassName)}>&bull;</span>
        })}
      </span>
    );
  };

  // 渲染pop文字
  renderPopText = ({ count, text = '', cpn = undefined }: IRenderPopText) => (
    <span>
      {cpn}
      <PopEllipsisText count={count} text={text} className="ellipsisText" />
    </span>
  );

  // 附加信息，比如拼团订单，周期购等
  renderExtraInfo = () => {
    const { orderTypeDesc, marketingTypeDesc } = this.props.mainOrderInfo;
    const { orderSource } = this.props.sourceInfo;
    const { groupIsHeader } = this.props.buyerInfo || {};
    const orderTypeText = groupIsHeader ? `${orderTypeDesc}(团长)` : orderTypeDesc;

    const textArr = [];
    // 零售单店/lite版不展示导购员标签
    // 零售单店不展示分销员标签
    if (
      !isRetailSingleStore &&
      marketingTypeDesc &&
      (!hasLiteAbility || marketingTypeDesc === '分销员')
    ) {
      textArr.push(marketingTypeDesc);
    }
    if (orderTypeText) {
      textArr.push(orderTypeText);
    }
    if (orderSource) {
      textArr.push(orderSource);
    }

    return <span className="basic">{textArr.join('/')}</span>;
  };

  isAllowDelivery = () => {
    const { operations } = this.props;
    return operations.some(operation => operation.code === 'delivery' && !operation.disabled);
  };

  renderDispatchCheckbox() {
    const { buyWay, feedback, orderType, isPeriodBuy } = this.props.mainOrderInfo;
    const { selfFetchVerifyType } = this.props.orderAddressInfo;
    const { needDispatchCheckbox, checked, onItemCheckBoxChange } = this.props;
    // 退款中，货到付款，周期购，送礼订单，显示当时不能选择 , 249 全额退款成功 250 买家主动撤销退款(维权结束)
    const disabled =
      buyWay === 9 || // 货到付款
      (feedback && feedback > 0 && feedback !== 249 && feedback !== 250) || // 退款中的订单
      orderType === 1 || // 是否是送礼单
      isPeriodBuy || // 是否是周期购
      !this.isAllowDelivery() || // 判断operation code 和 disabled
      selfFetchVerifyType === 0; // 有码核销的自提订单

    return needDispatchCheckbox ? (
      <span className="basic">
        <Checkbox checked={checked} onChange={onItemCheckBoxChange} disabled={disabled} />
      </span>
    ) : null;
  }

  // 操作按钮渲染
  renderOpt = () => {
    if (this.props.noOption) return null;
    const {
      mainOrderInfo: { orderNo, kdtId, expressType, state, orderType, saleWay },
      fulfillOrder: { fulfillNo, deliveryNo },
      isFulfillOrder,
      remark,
      remarkPics,
      withoutRemark,
      needModifyReceiverInfo = true,
      sourceInfo: { orderMark },
      founderToMemberMark
    } = this.props;
    const detailOrderUrl = isFulfillOrder
      ? `/v2/order/fulfilldetail#/?fulfillNo=${fulfillNo}`
      : `/v2/order/orderdetail#/?order_no=${orderNo}`;

    /**
     * 支持修改订单:
     * 仅零售单店和连锁L支持
     * 仅待付款和待发货状态支持
     * 前置仓不支持
     * 门店下订单的销售门店和当前B端的门店不一致则不支持
     * 送礼/酒店/周期购/社区团购订单不支持
     */
    const supportModifyReceiverInfo =
      (isRetailSingleStore || isUnifiedShop) &&
      [
        OrderStateType.ToPay,
        OrderStateType.ToSend,
        /**
         * 同城配送，已发货支持修改地址
         * @see https://xiaolv.qima-inc.com/#/newDailyDemand/dailyDemand#145865
         */
        ...(expressType === ExpressType.City ? [OrderStateType.Send] : [])
      ].includes(state as OrderStateType) &&
      !isFrontWarehouse &&
      /**
       * 门店支持修改网店+门店订单（仅支持买家下单为当前门店的订单）
       * 效率链接: https://xiaolv.qima-inc.com/#/demand/search?show=true&ids=154188
       */
      !(isUnifiedOfflineBranchStore && _global.kdtId !== kdtId) &&
      orderType !== ORDER_TYPE.GIFT.value &&
      orderType !== ORDER_TYPE.HOTEL.value &&
      orderType !== ORDER_TYPE.PERIOD.value &&
      orderType !== ORDER_TYPE.COMMUNITY_GROUPON.value &&
      checkAccess('修改订单');

    const isQttOrder = orderMark === 'quntuantuan';

    return (
      <span>
        <BlankLink href={detailOrderUrl}>查看详情</BlankLink>
        {!withoutRemark && (
          <>
            {' - '}
            <OptComponent
              operation={{ code: 'remark' }}
              options={{
                reload: this.handleRemarkChange,
                reloadQtt: this.handleFounderRemarkChange,
                isFulfillOrder,
                deliveryNo,
                orderNo,
                remark,
                remarkPics,
                founderToMemberMark,
                isQttOrder,
                disabled: notOptRemarkAndStar(kdtId) // TODO: 这里的逻辑实际上没有生效, 需要处理一下
              }}
            />
          </>
        )}
        {supportModifyReceiverInfo &&
          needModifyReceiverInfo &&
          (isRetailSingleStore || isUnifiedShop) && (
            <>
              {' - '}
              <ModifyReceiverInfo
                style={{ marginTop: -2 }}
                fulfillNo={fulfillNo}
                expressType={expressType}
                orderNo={orderNo}
                orderState={state}
              />
            </>
          )}
      </span>
    );
  };

  // 渲染商品超卖
  renderOversold = () => {
    const { isOversold } = this.props.mainOrderInfo;
    return isOversold ? <span className="basic red">商品已超卖，可进行主动退款</span> : null;
  };

  renderOversoldIcon = () => {
    return (
      <Pop
        trigger="hover"
        position="top-left"
        content="商品开启了付款减库存，购买人数过多造成超卖，需与买家协商退款或补货。"
        className="oversold-pop"
      >
        <Tag theme="yellow" className="oversold-icon">
          超卖订单
        </Tag>
      </Pop>
    );
  };

  /**
   * 扫码点单订单增加桌号展示
   */
  renderTableNum() {
    const { tableNum } = this.props.mainOrderInfo;
    return isNil(tableNum) || tableNum === '' ? null : (
      <span className="basic">{`桌号：${tableNum}`}</span>
    );
  }

  /**
   * 渲染视频号名称
   */
  renderPromoterNickname() {
    const { extraInfo } = this.props.mainOrderInfo;

    // 避免额外信息为 undefined
    if (!extraInfo) return null;

    const BIZ_ORDER_ATTRIBUTE = JSON.parse(extraInfo)?.BIZ_ORDER_ATTRIBUTE;

    if (!BIZ_ORDER_ATTRIBUTE) return null;

    const PROMOTER_NICK_NAME = JSON.parse(BIZ_ORDER_ATTRIBUTE)?.PROMOTER_NICK_NAME;

    if (!PROMOTER_NICK_NAME) return null;

    return <span className="basic">{`视频号名称：${PROMOTER_NICK_NAME}`}</span>;
  }

  /**
   * 渲染美团订单相关的信息
   */
  renderMeituanExtraInfo() {
    const { extraInfo, reminderStatus } = this.props.mainOrderInfo;

    // 避免额外信息为 undefined
    if (!extraInfo) return null;

    const PICK_UP_CODE = JSON.parse(extraInfo)?.PICK_UP_CODE;

    if (!PICK_UP_CODE) return null;

    return (
      <span className="basic">
        {`取货号：${PICK_UP_CODE}`}
        {!isNil(reminderStatus) && +reminderStatus === 1 ? (
          <>
            &nbsp;&nbsp;<Tag>催单</Tag>
          </>
        ) : null}
      </span>
    );
  }

  /**
   * 渲染供应商名称
   */
  renderSupplierName() {
    const { mainOrderInfo, supplierInfo } = this.props;
    const tagsInfo = deepJsonParse(mainOrderInfo.tagsInfo);

    if (tagsInfo?.IS_FENXIAO_ORDER && supplierInfo) {
      return <SupplierName supplierInfo={supplierInfo} />;
    }

    return null;
  }

  /**
   * 渲染群团团跟团号
   */
  renderQttParticipateNo() {
    const { mainOrderInfo } = this.props;
    const participateNo = get(mainOrderInfo, 'extra.aTTR_FX_ZPP_FOUNDER_PARTICIPATE_NO');

    if (!participateNo) {
      return null;
    }

    return (
      <span className="basic">
        群团团跟团号：
        {participateNo}
      </span>
    );
  }

  render() {
    const { selfFetchInfo = {} as ISelfFetchInfo, expectDeliveryTimeDesc = '' } =
      this.props.orderAddressInfo;

    const {
      expressType,
      expressTypeDesc,
      orderTypeDesc,
      orderNo,
      createTime,
      outBizNo,
      kdtId,
      shopName,
      saleWayDesc,
      riskDesc,
      riskTips,
      prescriptionNo,
      icons = []
    } = this.props.mainOrderInfo;
    const isSelfFetch = expressType === ExpressType.SelfFetch;
    // 离线订单编号
    const offlineOrderNo = this.isOfflineOrder(this.props.mainOrderInfo) ? outBizNo : '';
    // 是否显示超卖标签
    const showOversoldIcon = icons.find((item: Icons) => {
      return item.code === 'order_stock_over';
    });

    const { outerTransactionNumberList = [], innerTransactionNumberList = [] } =
      this.props.paymentInfo;

    const { withSaleWay, withExtraInfo, withExpress, withOrderType, exchangeInfo } = this.props;
    const { sourceOrderNo, exchangeNo } = exchangeInfo || {};

    // 显示更多
    const isShowMore =
      this.props.withMore &&
      (outerTransactionNumberList.length > 0 ||
        innerTransactionNumberList.length > 0 ||
        offlineOrderNo);
    const isExchangeOrder = Boolean(exchangeNo);
    const orderType = isExchangeOrder ? 'exchange' : 'normal';
    return (
      <div className="order-item__header">
        <div className={`basic-info${sourceOrderNo ? ' header-basic-info' : ''}`}>
          <span className="info-wrapper">
            {this.renderDispatchCheckbox()}
            {riskDesc && riskTips ? (
              <Pop
                trigger="click"
                wrapperClassName="risk"
                position="top-left"
                content={<div className="risk-content">{riskTips}</div>}
              >
                <span className="risk-tag">{riskDesc}</span>
              </Pop>
            ) : null}
            {showOversoldIcon && this.renderOversoldIcon()}
            <span className="basic">
              {OrderNoTitleRecord[orderType]}：{orderNo}
              {sourceOrderNo && (
                <>
                  <br />
                  原订单号：{sourceOrderNo}
                </>
              )}
            </span>
            <BasicWrap sourceOrderNo={sourceOrderNo}>
              {isShowMore && (
                <Pop
                  trigger="click"
                  position="bottom-left"
                  content={
                    <div>
                      {prescriptionNo && (
                        <p>
                          {`处方单号：${prescriptionNo} `}
                          <BlankLink
                            href={setUrlDomain(
                              `/v2/order/prescription#/detail/${kdtId}/${prescriptionNo}`,
                              'store'
                            )}
                          >
                            详情
                          </BlankLink>
                        </p>
                      )}
                      {offlineOrderNo && <p>离线订单编号: {offlineOrderNo}</p>}
                      {outerTransactionNumberList[0] && (
                        <p>外部订单编号: {outerTransactionNumberList[0]}</p>
                      )}
                      {innerTransactionNumberList[0] && (
                        <p>支付流水号: {innerTransactionNumberList[0]}</p>
                      )}
                    </div>
                  }
                >
                  <span className="link-blue-color basic">更多</span>
                </Pop>
              )}
              <span className="basic">
                下单时间：
                {formatDatetime(createTime ?? '')}
              </span>
              {withSaleWay &&
                !isUnifiedShop &&
                this.renderSaleWay({
                  count: withExtraInfo ? 14 : 18,
                  text: this.getShopName()!
                })}
              {withSaleWay &&
                isUnifiedShop &&
                this.renderSaleWay({
                  count: withExtraInfo ? 14 : 18,
                  text: saleWayDesc
                })}
              {isUnifiedShop && <span className="basic">{shopName}</span>}
              {withExtraInfo && this.renderExtraInfo()}
              {withOrderType && (
                <span className="basic">
                  订单类型：
                  <span className="express-way">{orderTypeDesc}</span>
                </span>
              )}
              {!isSelfFetch && withExpress && (
                <span className="basic">
                  配送方式：
                  <span className="express-way">{expressTypeDesc}</span>
                </span>
              )}
              {expectDeliveryTimeDesc !== '' && this.props.withExpectDeliveryTime && (
                <span className="basic">
                  定时送达时间：
                  <span className="color-orange">{expectDeliveryTimeDesc}</span>
                </span>
              )}
              {isSelfFetch && this.props.withSelfFetchAddr && (
                <>
                  <span className="basic">
                    自提时间：
                    <span className="color-orange">{selfFetchInfo.userTime}</span>
                  </span>
                  <span className="basic">
                    自提地点：
                    {this.renderPopText({
                      count: 10,
                      text: selfFetchInfo.name
                    })}
                  </span>
                </>
              )}
              {this.props.withOversold && this.renderOversold()}
              {/* 扫码点单订单展示桌号 */}
              {this.renderTableNum()}
              {this.renderMeituanExtraInfo()}
              {this.renderQttParticipateNo()}
              {this.renderSupplierName()}
              {this.renderPromoterNickname()}
            </BasicWrap>
          </span>

          <span className="align-right light-grey">
            {this.props.withStar ? (
              <StarOpener
                noMouseEvent={notOptRemarkAndStar(kdtId)}
                star={this.props.star}
                onStarChange={this.handleStarChange}
              >
                {this.renderOpt()}
                {' - '}
              </StarOpener>
            ) : (
              this.renderOpt()
            )}
          </span>
        </div>
      </div>
    );
  }
}

export default ItemHeader;
