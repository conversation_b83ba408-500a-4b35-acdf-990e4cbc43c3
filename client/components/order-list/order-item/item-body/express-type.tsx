import * as React from 'react';
import { get } from 'lodash';
import { IMainOrderInfo } from 'definition/order-info';

export interface IExpressType {
  mainOrderInfo: IMainOrderInfo;
}

/**
 * 发货方式显示
 * props: {
 *  mainOrderInfo
 * }
 * @param {any} props
 * @returns
 */
const ExpressType = (props: IExpressType) => (
  <div className="cell cell--express-type">{get(props, 'mainOrderInfo.expressTypeDesc', '')}</div>
);

export default ExpressType;
