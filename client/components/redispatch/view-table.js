import React, { useState, useEffect } from 'react';
import { Grid, Notify, Pop, Button } from 'zent';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain, formatDatetime } from '@youzan/retail-utils';

import { fetchDispatchRecords } from './api';
import { Wrapper, DisabledTrigger } from './style';
import { style } from './style.scss';

export default function RedispatchViewTable(props) {
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const { data, setGoodsData } = props;
  const { orderNo, itemIdStr } = data;

  useEffect(() => {
    setLoading(true);

    fetchDispatchRecords({ orderNo, orderItemId: itemIdStr })
      .then(res => {
        setRecords(res.reDispatchWarehouseRecordList || []);
        setGoodsData({
          ...data,
          isFulfillOrderExist: res.isSourceFulfillOrderExist,
          fulfillNos: [res.sourceFulfillNo],
          warehouseNames: [res.sourceWarehouseName]
        });
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        setLoading(false);
      });
  }, []); // eslint-disable-line

  return (
    <Wrapper>
      <Grid
        rowKey="warehouseId"
        datasets={records}
        loading={loading}
        className={style}
        scroll={{ x: 1000 }}
        columns={[
          {
            title: '改派时间',
            name: 'operateTime',
            bodyRender({ operateTime }) {
              return formatDatetime(operateTime);
            }
          },
          {
            title: '改派人',
            name: 'operatorName',
            bodyRender({ operatorName }) {
              return operatorName || '-';
            }
          },
          {
            title: '改派门店/仓库',
            name: 'warehouseName'
          },
          {
            title: '发货数量',
            width: '10%',
            name: 'deliveryNum'
          },
          {
            title: '发货单号',
            width: '25%',
            textAlign: 'right',
            bodyRender({ fulfillNo, valid }) {
              if (!valid) {
                return (
                  <Pop trigger="click" content="该发货单已改派，无法查看详情">
                    <DisabledTrigger>{fulfillNo}</DisabledTrigger>
                  </Pop>
                );
              }

              return (
                <BlankLink
                  href={setUrlDomain(`/v2/order/fulfilldetail#/?fulfillNo=${fulfillNo}`, 'store')}
                >
                  {fulfillNo}
                </BlankLink>
              );
            }
          }
        ]}
      />
      <footer className="footer">
        <Button onClick={props.onClose}>取消</Button>
      </footer>
    </Wrapper>
  );
}
