/**
 * 改派弹窗 - 仓库 Table
 */

import React, { useState, useEffect, useReducer, useContext } from 'react';
import { Grid, Button, Notify, NumberInput } from 'zent';
import { Select } from '@zent/compat';
import cx from 'classnames';
import { get, isNil } from 'lodash';
import { div } from '@youzan/retail-utils';
import { WarnContext } from './context';
import {
  transformWarehouseData,
  patchWarehouseData,
  getInsertRowData,
  checkWarehouseData,
  warehousesReducer
} from './helper';
import { fetchWarehouseList, excuteRedispatch } from './api';
import { Wrapper, ButtonWrapper, AddButton } from './style';
import { style } from './style.scss';

const initialState = {
  current: [],
  raw: [],
  avaliable: []
};

const MAX_WAREHOUSE_NUM = 10;

/**
 * 判断是否称重商品
 *
 * @param {Object} goods
 */
const getIsWeightedItem = goods => +get(goods, 'pricingStrategy') === 10;

export default function DispatchTable(props) {
  const { data, onClose, onChanged } = props;
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [warehouseObject, dispatch] = useReducer(warehousesReducer, initialState);
  const { setWarn } = useContext(WarnContext);
  const [latestIndex, setLatestIndex] = useState(0);
  const [showError, setShowError] = useState(false);

  const { avaliable, current, raw } = warehouseObject;
  const isWeightedItem = getIsWeightedItem(data);
  const currentNum = div(data.num, 1000);

  useEffect(() => {
    if (current.length > 0) {
      const index =
        latestIndex < 0 || latestIndex >= current.length ? current.length - 1 : latestIndex;
      const { warnTips = '' } = current[index];
      setWarn(warnTips);
    } else {
      setWarn('');
    }
  }, [current, latestIndex, setWarn]);

  useEffect(() => {
    setLoading(true);

    fetchWarehouseList({
      orderNo: data.orderNo,
      fulfillNo: get(data, 'fulfillNos[0]'),
      orderItemId: data.itemIdStr
    })
      .then(res => {
        const warehouseRawData = res.warehouseSelectionInfos;

        if (warehouseRawData.length !== 0) {
          const warehouseList = transformWarehouseData(warehouseRawData || []);
          dispatch({
            type: 'initialize',
            payload: {
              // 原始数据
              raw: warehouseList,
              // 当前数据
              current: [getInsertRowData(warehouseList, [])].map(item => ({
                ...item,
                amount: currentNum
              })),
              // 可选数据
              avaliable: [warehouseList]
            }
          });
        }
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        setLoading(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <Wrapper>
      <Grid
        className={style}
        rowKey="warehouseId"
        datasets={current}
        loading={loading}
        emptyLabel={raw.length > 0 ? '' : '没有可改派的发货方'}
        columns={[
          {
            title: '发货门店/仓库',
            width: '40%',
            bodyRender: ({ warehouseId }, { row }) => {
              return (
                <Select
                  value={warehouseId}
                  data={avaliable[row]}
                  width={240}
                  filter={(item, keyword) => {
                    return item.text.indexOf(keyword) > -1;
                  }}
                  onChange={e => {
                    dispatch({
                      type: 'change',
                      payload: patchWarehouseData(raw, current, row, 'warehouseId', e.target.value)
                    });
                    setLatestIndex(row);
                  }}
                />
              );
            }
          },
          {
            title: '库存数量',
            name: 'stock',
            width: '15%',
            bodyRender: ({ stock }) => {
              return isNil(stock) ? '--' : stock;
            }
          },
          {
            title: '发货数量',
            name: 'amount',
            width: '35%',
            bodyRender: ({ amount = '' }, { row }) => {
              if (isWeightedItem) {
                return currentNum;
              }

              return (
                <div className={cx('amount', { 'amount-error': showError && amount === '' })}>
                  <NumberInput
                    integer
                    min={1}
                    value={amount}
                    width={100}
                    onChange={value => {
                      dispatch({
                        type: 'change',
                        payload: patchWarehouseData(raw, current, row, 'amount', value)
                      });
                      setShowError(true);
                    }}
                  />
                  {showError && amount === '' && <p className="amount-invalid">发货数量不能为空</p>}
                </div>
              );
            }
          },
          {
            title: '操作',
            width: '10%',
            textAlign: 'right',
            bodyRender: (rowData, { row }) => {
              if (isWeightedItem) return '--';

              return (
                <a
                  onClick={() => {
                    dispatch({
                      type: 'delete',
                      payload: row
                    });
                  }}
                >
                  删除
                </a>
              );
            }
          }
        ]}
      />
      {!isWeightedItem && current.length < raw.length && current.length < MAX_WAREHOUSE_NUM ? (
        <ButtonWrapper>
          <AddButton
            onClick={() => {
              setLatestIndex(-1);
              dispatch({ type: 'add' });
            }}
          >
            <span className="icon" />
            添加
          </AddButton>
        </ButtonWrapper>
      ) : null}
      <footer className="footer">
        <Button loading={submitting} onClick={() => onClose()}>
          取消
        </Button>
        <Button
          type="primary"
          disabled={current.length === 0}
          loading={submitting}
          onClick={() => {
            setShowError(true);

            const checkResult = checkWarehouseData(current, div(data.num, 1000), isWeightedItem);

            if (checkResult.success) {
              setSubmitting(true);
              excuteRedispatch(current, data)
                .then(() => {
                  Notify.success('改派成功');
                  onChanged();
                })
                .catch(err => {
                  Notify.error(err.msg || '改派失败');
                })
                .finally(() => {
                  setSubmitting(false);
                });
            } else {
              Notify.error(checkResult.msg);
            }
          }}
        >
          确认改派
        </Button>
      </footer>
    </Wrapper>
  );
}
