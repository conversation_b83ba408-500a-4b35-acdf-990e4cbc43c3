import { get, isNil } from 'lodash';
import { plus } from '@youzan/retail-utils';

/**
 * 格式化初始的仓库数据
 *
 * @param {Array<Object>} data
 *
 * @returns {Array<Object>}
 */
export function transformWarehouseData(data) {
  return data.map(({ warehouseId, supportItemNum, warehouseName, warnTips }) => ({
    warehouseId,
    warnTips,
    value: warehouseId,
    text: warehouseName,
    stock: supportItemNum
  }));
}

/**
 * 根据入参生成新的选中的仓库列表数据
 *
 * @param {Array<Object>} raw
 * @param {Array<Object>} origin
 * @param {Number} index
 * @param {String} type
 * @param {String} value
 *
 * @returns {Array<Object>}
 */
export function patchWarehouseData(raw, origin, index, type, value) {
  const patched = origin.slice();
  const patchValue =
    type === 'warehouseId' ? raw.find(item => item.warehouseId === value) : { [type]: value };
  patched.splice(index, 1, { ...origin[index], ...patchValue });
  return patched;
}

/**
 * 获取当前可选的仓库名称
 *
 * @param {Array<Object>} raw
 * @param {Array<Object>} selected
 *
 * @returns {Array<Object>}
 */
export function getCurrentWarehouses(raw, selected) {
  return raw.filter(warehouse => !selected.find(w => w.warehouseId === warehouse.warehouseId));
}

/**
 * 获取当前默认的插入数据
 *
 * @param {Array<Object>} raw
 * @param {Array<Object>} selected
 *
 * @returns {Object}
 */
export function getInsertRowData(raw, selected) {
  return get(getCurrentWarehouses(raw, selected), '[0]');
}

/**
 * 生成可选的仓库数据列表
 *
 * @param {Array<Object>} raw
 * @param {Array<Object>} current
 *
 * @returns {Array<Array<Object>>}
 */
function getAvaliableWarehouses(raw, current) {
  // 新增的时候，移除原来 avaliable 项中对应的元素
  const currentWarehouseIds = current.map(item => item.warehouseId);

  return currentWarehouseIds
    .map(() => raw)
    .map((arr, x) => {
      const excludeWarehouseIds = current
        .filter((item, y) => x !== y)
        .map(item => item.warehouseId);

      return arr.filter(warehouse => !excludeWarehouseIds.includes(warehouse.warehouseId));
    });
}

/**
 * 校验仓库数据
 *
 * @param {Array<Object>} warehouses
 * @param {Number} num
 * @param {Boolean} isWeightedItem
 */
export function checkWarehouseData(warehouses, num, isWeightedItem) {
  const MSG = '改派数量必须小于等于发货数量且小于等于供货库存数量';

  // 0. 称重商品直接判断当前库存是否够用
  if (isWeightedItem) {
    if (num >= warehouses.reduce((prev, next) => plus(+prev, +next.stock), 0)) {
      return {
        success: false,
        msg: MSG
      };
    }

    return {
      success: true,
      msg: ''
    };
  }

  // 0. 发货数量必填
  if (warehouses.some(({ amount }) => !+amount)) {
    return {
      success: false,
      msg: '请填写发货数量'
    };
  }

  // 1. 校验每个仓库的发货数量是否小瑜等于供货仓数量
  if (
    warehouses.some(warehouse => {
      if (isNil(warehouse.stock)) return false;
      if (+warehouse.stock < +warehouse.amount) return true;
      return false;
    })
  ) {
    return {
      success: false,
      msg: MSG
    };
  }

  // 2. 校验总数
  const total = warehouses.reduce((prev, next) => plus(+prev, +next.amount), 0);

  if (total > num) {
    return {
      success: false,
      msg: MSG
    };
  }

  return {
    success: true,
    msg: ''
  };
}

/**
 * 仓库选择 Reducer
 *
 * @param {Object} state
 * @param {Object} action
 *
 * @returns {Object} new state
 */
export function warehousesReducer(state, action) {
  switch (action.type) {
    // 添加仓库
    case 'add': {
      const addedRow = getInsertRowData(state.raw, state.current);
      const current = state.current.concat(addedRow);

      return {
        ...state,
        avaliable: getAvaliableWarehouses(state.raw, current),
        current: state.current.concat(addedRow)
      };
    }
    // 删除仓库
    case 'delete': {
      const updatedCurrent = state.current.slice();
      updatedCurrent.splice(action.payload, 1);
      return {
        ...state,
        avaliable: getAvaliableWarehouses(state.raw, updatedCurrent),
        current: updatedCurrent
      };
    }
    // 仓库选择变更
    case 'change': {
      return {
        ...state,
        avaliable: getAvaliableWarehouses(state.raw, action.payload),
        current: action.payload
      };
    }
    // 初始化
    case 'initialize':
      return action.payload;
    default:
      throw new Error('wrong action type');
  }
}
