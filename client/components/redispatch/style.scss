@import '~shared/style';

:local(.style) {
  .zent-grid-td {
    border-right: none;
    padding: 22px 12px;
  }

  .amount {
    position: relative;

    &.amount-error .zent-input-wrapper {
      border-color: $border-color-alert;

      &.zent-input--has-focus:not(.zent-input-wrapper__not-editable) {
        box-shadow: 0 0 4px 0 rgba($color: $border-color-alert, $alpha: 0.2);
      }
    }
  }

  .amount-invalid {
    position: absolute;
    bottom: -22px;
    font-size: $font-size-small;
    color: $border-color-alert;
  }
}

:local(.redispatch) {
  .redispatch-alert {
    margin-bottom: 20px;
  }
}

.redis-patch__goods-info {
  &.goods-info {
    padding-left: 0;
    flex: 1;
  }
}
