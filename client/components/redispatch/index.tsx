/**
 * 改派弹窗组件
 *
 * <AUTHOR>
 */
import type { Contains } from 'definition/common';
import * as React from 'react';
import { Dialog, Alert, Sweetalert } from 'zent';

import { EProcessStatus } from 'route/process-setting/types';
import WarnContextProvider, { WarnContext } from './context';
import Goods from './goods';
import Table from './table';
import ViewTable from './view-table';
import { Trigger } from './style';
import { redispatch } from './style.scss';

const { useState } = React;

function getText(type: string, isTitle: boolean) {
  const prefix = `${isTitle ? '商品' : ''}改派`;
  if (type === 'view') return `${prefix}记录`;
  if (type === 'create') return prefix;
  return '';
}

export default function Redispatch(
  props: Contains<{ goods: unknown; type: 'view' | 'create'; onClose: () => void }>
): JSX.Element {
  const [visible, setVisible] = useState(false);
  const [goodsData, setGoodsData] = useState(props.goods);
  const { type, onClose } = props;

  const onChanged = () => {
    setVisible(false);
    setTimeout(() => onClose(), 500);
  };

  const onDialogClose = () => {
    setVisible(false);
  };

  return (
    <>
      <Trigger
        onClick={(): void => {
          const { processStatus } = props.goods as any;
          if (
            [
              EProcessStatus.ManufactureWait,
              EProcessStatus.Manufacturing,
              EProcessStatus.AllotWait,
              EProcessStatus.Alloting
            ].includes(processStatus)
          ) {
            Sweetalert.confirm({
              content: <p>该商品制作中，请确认是否要改派？</p>,
              onConfirm: () => setVisible(true)
            });
          } else {
            setVisible(true);
          }
        }}
      >
        {getText(type, false)}
      </Trigger>
      <Dialog
        visible={visible}
        title={getText(type, true)}
        onClose={(): void => setVisible(false)}
        style={{ width: '860px' }}
        className={redispatch}
      >
        <WarnContextProvider>
          <WarnContext.Consumer>
            {({ warn }: { warn: boolean }): JSX.Element | null =>
              warn ? (
                <Alert type="warning" className="redispatch-alert">
                  {warn}
                </Alert>
              ) : null
            }
          </WarnContext.Consumer>
          <Goods data={goodsData} />
          {type === 'create' ? (
            <Table data={goodsData} onChanged={onChanged} onClose={onDialogClose} />
          ) : (
            <ViewTable
              data={goodsData}
              onChanged={onChanged}
              onClose={onDialogClose}
              setGoodsData={setGoodsData}
            />
          )}
        </WarnContextProvider>
      </Dialog>
    </>
  );
}
