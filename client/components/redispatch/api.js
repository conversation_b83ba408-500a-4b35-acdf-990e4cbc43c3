import { request } from '@youzan/retail-utils';
import { get } from 'lodash';

/**
 * 获取改派记录
 *
 * @param {Object} params
 */
export const fetchDispatchRecords = ({ orderNo, orderItemId }) => {
  return request({
    url: '/youzan.retail.trademanager.redispatch.record/1.0.0/get',
    data: {
      orderNo,
      orderItemId
    }
  });
};

/**
 * 获取仓库列表
 */
export const fetchWarehouseList = ({ orderNo, orderItemId, fulfillNo }) => {
  return request({
    url: '/youzan.retail.trademanager.redispatchwindow/1.0.0/get',
    data: {
      orderNo,
      orderItemId,
      fulfillNo
    }
  });
};

/**
 * 执行改派
 *
 * @param {Array<Object>} warehouses
 * @param {Object} goods
 */
export const excuteRedispatch = (warehouses, goods) => {
  const isWeightedItem = +goods.pricingStrategy === 10;
  return request({
    url: '/youzan.retail.trademanager/1.0.0/changedispatcher',
    data: {
      orderNo: goods.orderNo,
      orderItemId: goods.itemIdStr,
      isWeight: isWeightedItem,
      fulfillNo: get(goods.fulfillNos, '[0]'),
      changeDetails: warehouses.map(warehouse => {
        return {
          changeNum: isWeightedItem ? goods.num : warehouse.amount,
          wareHouseId: warehouse.warehouseId
        };
      })
    }
  });
};
