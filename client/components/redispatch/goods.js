/**
 * 改派弹窗商品展示
 */

import React from 'react';
import { get } from 'lodash';
import { div, setUrlDomain } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import GoodsInfo from 'components/goods-info';

import { Container, Amount, GoodsWrapper, OriginStockInfo } from './style';

export default function Goods(props) {
  const {
    num,
    unit,
    title,
    imgUrl,
    skuDesc,
    goodsNo,
    fulfillNos,
    warehouseNames,
    isFulfillOrderExist = true
  } = props.data;
  const originFulfillNo = get(fulfillNos, '[0]');

  return (
    <Container>
      <GoodsWrapper>
        <GoodsInfo
          imgUrl={imgUrl}
          title={title}
          skuDesc={skuDesc}
          goodsNo={goodsNo}
          isShowGoodsNo
          className="redis-patch__goods-info"
        />
        <OriginStockInfo>
          <p>{`原发货仓：${get(warehouseNames, '[0]')}`}</p>
          <p>
            发货单号：
            {isFulfillOrderExist ? (
              <BlankLink
                href={setUrlDomain(
                  `/v2/order/fulfilldetail#/?fulfillNo=${originFulfillNo}`,
                  'store'
                )}
              >
                {originFulfillNo}
              </BlankLink>
            ) : (
              originFulfillNo
            )}
          </p>
        </OriginStockInfo>
        <Amount>
          <span className="num">{div(num, 1000)}</span>
          {unit}
        </Amount>
      </GoodsWrapper>
    </Container>
  );
}
