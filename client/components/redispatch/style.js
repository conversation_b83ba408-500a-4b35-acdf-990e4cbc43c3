import styled from 'styled-components';

const mainColor = '#155bd4';

export const Trigger = styled.p`
  color: ${mainColor};
  cursor: pointer;
`;

export const DisabledTrigger = styled.span`
  color: #999;
  cursor: pointer;
`;

export const Wrapper = styled.div`
  position: relative;
  margin-top: 20px;

  .footer {
    margin-top: 20px;
    text-align: right;
    width: 100%;
    position: static;
  }
`;

export const AddButton = styled.span`
  display: inline-flex;
  align-items: center;
  color: ${mainColor};
  font-size: 14px;
  cursor: pointer;

  .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 16px;
    width: 16px;
    border-radius: 0.5px;
    margin-right: 5px;
    border: 1px solid ${mainColor};

    &:before,
    &:after {
      display: inline-block;
      content: '';
      width: 1px;
      height: 10px;
      background-color: ${mainColor};
    }

    &:before {
      transform: translateX(0.5px);
    }

    &:after {
      transform: rotate(90deg);
    }
  }
`;

export const Container = styled.div`
  background-color: #f8f8f8;
  border: 1px solid #e5e5e5;
  padding: 16px;
`;

export const Title = styled.div`
  font-weight: bold;
  font-size: 14px;
  color: #323233;
`;

export const GoodsWrapper = styled.div`
  display: flex;
`;

export const OriginStockInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 20px;
  font-size: 14px;
  color: #333;
`;

export const Amount = styled.div`
  color: #333;
  font-weight: bold;
  align-self: center;

  .num {
    font-size: 18px;
  }
`;

export const ButtonWrapper = styled.div`
  padding: 16px;
  box-shadow: inset 0 -1px 0 0 #ebedf0;
`;
