import React from 'react';
import cx from 'classnames';
import { formatDatetime } from '@youzan/retail-utils';
import { Button } from 'zent';
import { noop, get } from 'lodash';
import withDialog from 'components/with-dialog';
import LineContent from 'components/line-content';
import ChatWindow from './chat-window';
import { sendReminderMsg } from './api';
import style from './style.scss';

@withDialog
class Reminder extends React.Component {
  static defalutProps = {
    orderInfo: {},
    reload: noop
  };

  get chatProps() {
    const { orderInfo, reload } = this.props;
    const chatRes = get(orderInfo, 'remarkInfo.reminderItems', []);

    const chatHistory = chatRes.reduce((pre, { reminderTime, reminderRspList }) => {
      const buyerContent = {
        time: reminderTime,
        isSelf: false,
        content: '发起催单'
      };

      if (reminderRspList.length === 0) {
        return pre.concat([buyerContent]);
      }

      const sellerContents = reminderRspList.map(({ content, replyTime }) => ({
        time: replyTime,
        content,
        isSelf: true
      }));

      return pre.concat([buyerContent, ...sellerContents]);
    }, []);

    const remindId = chatRes.length > 0 ? chatRes[chatRes.length - 1].id : null;

    const sendMessageAjax = replyContent =>
      sendReminderMsg({
        remindId,
        replyContent,
        orderNo: get(orderInfo, 'mainOrderInfo.orderNo')
      });

    return {
      chatHistory,
      sendMessageAjax,
      afterSendMsg: reload
    };
  }

  handleOpenDialog = () => {
    const {
      openDialog,
      orderInfo: {
        mainOrderInfo: { newState = 99 }
      }
    } = this.props;

    const children = (
      <ChatWindow disabled={newState === 99 || newState === 100} {...this.chatProps} />
    );

    openDialog({
      title: '回复催单',
      style: { width: 600 },
      className: 'chat-window-dialog',
      children
    });
  };

  getBtnText(lastChat) {
    const lastChatList = get(lastChat, 'reminderRspList[0]', null);
    if (lastChatList) {
      return '已回复';
    }
    return '回复催单';
  }

  render() {
    const { orderInfo, className } = this.props;
    const reminderInfo = get(orderInfo, 'remarkInfo.reminderItems', []);
    if (reminderInfo.length === 0) {
      return null;
    }
    const lastChat = reminderInfo[reminderInfo.length - 1];

    return (
      <LineContent
        className={cx(style['remind-info'], className)}
        text={formatDatetime(lastChat.reminderTime)}
        label="买家催单"
        right={<Button onClick={this.handleOpenDialog}>{this.getBtnText(lastChat)}</Button>}
      />
    );
  }
}

export default Reminder;
