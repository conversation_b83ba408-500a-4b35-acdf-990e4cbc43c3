@import '~shared/style';

:local(.chat-window) {
  .chat-content {
    background-color: $background-color-base;
    padding: 25px 30px;
    margin-bottom: 20px;

    &-item {
      border: 1px solid $border-color-base;
      background: $color-white;
      padding: 10px 15px;
      clear: both;
      margin-bottom: 20px;
      min-width: 250px;

      &.msg-right {
        float: right;
      }

      &.msg-left {
        float: left;
      }
    }
    .clear {
      clear: both;
    }

    .wirter-info {
      font-size: 12px;
      margin-bottom: 3px;
    }
  }

  .chat-input {
    display: flex;
    padding: 0 20px;
    .zent-input-wrapper {
      flex: 1;
      margin-right: 10px;
    }

    .send-btn {
      width: 80px;
    }
  }
}

:local(.remind-info) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-window-dialog {
  padding: 20px 0;
  .zent-dialog-r-title {
    padding: 0 20px;
    border-bottom: none;
  }
}
