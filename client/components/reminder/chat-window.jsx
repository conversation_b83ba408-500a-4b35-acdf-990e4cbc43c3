import React from 'react';
import PropTypes from 'prop-types';
import { Input, Button, Notify } from 'zent';
import cx from 'classnames';

import { formatDatetime } from '@youzan/retail-utils';
import style from './style.scss';

class ChatWindow extends React.Component {
  state = {
    inputMsg: '',
    isSending: false,
    chatContents: this.props.chatHistory
  };

  static propTypes = {
    chatHistory: PropTypes.array,
    chatItemComponent: PropTypes.node,
    sendMessageAjax: PropTypes.func,
    onChange: PropTypes.func,
    afterSendMsg: PropTypes.func
  };

  static defaultProps = {
    chatHistory: []
  };

  handleSendMsg = () => {
    const { sendMessageAjax, afterSendMsg } = this.props;
    const { inputMsg, chatContents: prevContents } = this.state;

    if (!inputMsg) {
      return;
    }

    this.setState({ isSending: true });
    sendMessageAjax(inputMsg)
      .then(() => {
        const chatContents = prevContents.concat({
          isSelf: true,
          content: inputMsg,
          time: Date.now()
        });
        afterSendMsg && afterSendMsg();
        this.setState({ chatContents });
        Notify.success('回复成功！');
      })
      .catch(err => {
        Notify.error(err.msg || '回复失败！');
      })
      .finally(() => {
        this.setState({ isSending: false });
      });
  };

  handleMsgChange = e => this.setState({ inputMsg: e.target.value });

  render() {
    const { inputMsg, isSending, chatContents } = this.state;
    const { chatItemComponent, disabled } = this.props;

    return (
      <div className={style['chat-window']}>
        <div className="chat-content">
          {chatContents.map((chatItem, idx) => {
            const { isSelf, content, time } = chatItem;
            const className = isSelf ? 'msg-right' : 'msg-left';

            if (chatItemComponent) {
              const ChatItem = chatItemComponent;
              return <ChatItem {...chatItem} key={idx} />;
            }

            const writerRoleName = isSelf ? '商家回复' : '买家';
            return (
              <div className={cx('chat-content-item', className)} key={idx}>
                <p className="gray wirter-info">
                  {writerRoleName}-{formatDatetime(time)}
                </p>
                <p>{content}</p>
              </div>
            );
          })}
          <div className="clear" />
        </div>
        <div className="chat-input">
          <Input disabled={disabled} onChange={this.handleMsgChange} value={inputMsg} />
          <Button
            disabled={disabled}
            onClick={this.handleSendMsg}
            loading={isSending}
            className="send-btn"
            type="primary"
          >
            发送
          </Button>
        </div>
      </div>
    );
  }
}

export default ChatWindow;
