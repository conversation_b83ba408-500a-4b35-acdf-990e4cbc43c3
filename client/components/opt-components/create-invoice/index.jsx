import React from 'react';
import { Notify } from 'zent';
import { get } from 'lodash';
import { CloudSlot } from '@youzan/ranta-cloud-react';
import { dataWatcher } from 'route/order-detail/data-watcher';
import createOptComponent from '../create-opt-component';
import { checkInviceStatus } from './api';

@createOptComponent
class Remark extends React.Component {
  state = {
    disableInvoiceButton: false
  };

  openDialog = config => {
    const { options, operation } = config;
    const orderNo = get(options, 'orderInfo.mainOrderInfo.orderNo');
    checkInviceStatus(orderNo)
      .then(() => {
        const invoiceUrl = get(operation, 'attributes.url');
        window.open(invoiceUrl, '_blank');
      })
      .catch(err => {
        Notify.error(err.msg || '无法开票！');
      });
  };

  componentDidMount() {
    const { disableInvoiceButton } = dataWatcher.getData();
    this.setState({
      disableInvoiceButton
    });
    dataWatcher.listen(['disableInvoiceButton'], nextData => {
      this.setState({
        disableInvoiceButton: nextData.disableInvoiceButton
      });
    });
  }

  render() {
    const { OptTypeCpn } = this.props;
    const { disableInvoiceButton } = this.state;
    return (
      <CloudSlot cloudKey="invoice-button">
        {!disableInvoiceButton && <OptTypeCpn onClick={this.openDialog} />}
      </CloudSlot>
    );
  }
}

export default Remark;
