import React, { useRef, useState, useEffect, useMemo } from 'react';
import cx from 'classnames';

import style from './style.scss';

const Process = ({ dataSource = [], current = 1, className }) => {
  const containerRef = useRef();
  const stepRefs = useRef([]);
  const stepsDomWidths = useRef([]);
  const stepsDomWidth = useRef(0);
  const init = useRef(true);
  const [data, setData] = useState(dataSource);

  const initData = useMemo(() => {
    init.current = true;
    if (dataSource.length < 2) {
      return dataSource;
    }
    return dataSource.reduce((ret, item, index) => {
      if (index === dataSource.length - 1) {
        ret.push({ text: '...', step: '' });
      }
      ret.push(item);
      return ret;
    }, []);
  }, [dataSource]);

  useEffect(() => {
    if (dataSource.length < 2) {
      return;
    }
    if (init.current) {
      stepsDomWidths.current = stepRefs.current.map(dom => dom.offsetWidth);
      stepsDomWidth.current = stepsDomWidths.current.reduce((width, domWidth) => {
        return width + domWidth;
      }, 0);
      init.current = false;
    }

    const containerWidth = containerRef.current.offsetWidth;
    const stepsDomLen = stepsDomWidths.current.length;

    if (stepsDomWidth.current - stepsDomWidths.current[stepsDomLen - 2] > containerWidth) {
      let index = stepsDomLen - 3;
      let computeWidth = stepsDomWidth.current;
      const retData = [...initData];

      while (index >= 0 && computeWidth > containerWidth) {
        computeWidth -= stepsDomWidths.current[index];
        retData[index + 1].step = retData[index].step;
        retData.splice(index, 1);
        index -= 1;
      }
      setData(retData);
    } else {
      setData(dataSource);
    }
  }, [dataSource, initData]);

  const renderData = init.current ? initData : data;
  stepRefs.current = [];

  return (
    <div className={cx(style['process-container'], className)} ref={containerRef}>
      {renderData.map((item, index) => (
        <span
          ref={step => {
            step && stepRefs.current.push(step);
          }}
          key={item.step}
          className={cx('process-step', { 'process-complete': item.step <= current })}
        >
          {index !== 0 && <span className="process-icon-right">&gt;</span>}
          {item.text}
        </span>
      ))}
    </div>
  );
};

export default Process;
