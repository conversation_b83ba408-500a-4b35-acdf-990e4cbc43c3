import type { Contains } from 'definition/common';

import * as React from 'react';
import { isNil } from 'lodash';
import cx from 'classnames';
import { convertFenToYen } from 'common/fns/format';
import { getItemUmpInfo, combineUmpInfo } from 'common/helper';
import style from './style.scss';

function OrderUmpInfo({
  orderInfo,
  className
}: Contains<{
  orderInfo: Contains<{
    itemInfo: Array<Contains<{}>>;
    orderActivities?: unknown[];
    mainOrderInfo?: Contains<{}>;
  }>;
  className?: string;
}>) {
  const { itemInfo, orderActivities, mainOrderInfo } = orderInfo;

  // 相同活动的价格进行聚合
  const itemUmpInfo = getItemUmpInfo(itemInfo, mainOrderInfo);
  const combineOrderActivities = combineUmpInfo(orderActivities);

  const displayAvtivities = [...combineOrderActivities, ...itemUmpInfo];

  return (
    <div className={cx(style['order-ump-info'], 'grey', className)}>
      {displayAvtivities.map((umpInfo, index) => {
        const { displayDecrease, type, extraInfo, name, activityName } = umpInfo;
        const sumDecrease = isNil(displayDecrease)
          ? ''
          : `：-￥${convertFenToYen(displayDecrease)}`;

        // 满减送的优惠信息需要处理
        const meetReduceExtraInfo = [];
        if (type === 'meetReduce' && extraInfo) {
          // 满减送包邮
          +extraInfo.postage === 1 &&
            meetReduceExtraInfo.push(<p key="meet-reduce-postage">满减送：包邮</p>);

          // 满减送积分
          extraInfo.score &&
            meetReduceExtraInfo.push(
              <p key="meet-reduce-score">
                满减送：
                {extraInfo.score}
                积分
              </p>
            );

          // 满减送优惠券
          extraInfo.couponDefault &&
            meetReduceExtraInfo.push(
              <p key="meet-reduce-coupon">
                满减送：
                {extraInfo.couponDefault}
              </p>
            );
        }

        return (
          <div className="ump-info__item" key={index}>
            <p>{`${name || activityName}${sumDecrease}`}</p>
            {extraInfo && meetReduceExtraInfo}
          </div>
        );
      })}
    </div>
  );
}

OrderUmpInfo.defaultProps = {
  orderInfo: {}
};

export default OrderUmpInfo;
