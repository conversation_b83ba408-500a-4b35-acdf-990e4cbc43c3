import * as React from 'react';
import cx from 'classnames';
import { get } from 'lodash';
import { LayoutRow as Row, LayoutCol as Col, LayoutGrid as Grid } from 'zent';
import { deepJsonParse, formatDate } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain } from '@youzan/retail-utils';
import { WxControlNotice } from '@youzan/order-domain-pc-components';
import '@youzan/order-domain-pc-components/css/wx-control-notice/style.css';

import type {
  IMainOrderInfo,
  IOrderInfo,
  IQttOrderInfo,
  IAutoCallInfo,
  IOperation
} from 'definition/order-info';
import createOrderCpm from 'components/create-order-cpm';
import { ORDER_TYPE } from 'common/constants/order';
import { OrderStateType } from 'common/constants/common';

import { SupportParallelCallInLocalDelivery } from 'common/constant';
import StateInfoRenderer from './state-info';
import Actions, { ActionsProps } from './actions';
import Steps from './steps';
import Tips from './tips';
import Phase from './phase';
import style from './style.scss';

@createOrderCpm()
class OrderState extends React.Component<
  IOrderInfo & {
    reload: () => void;
    isQttOrder: boolean;
    qttOrderInfo: IQttOrderInfo;
  } & Pick<ActionsProps, 'withRemark' | 'withStar' | 'disabledOperationList'>
> {
  render() {
    const {
      mainOrderInfo = {} as IMainOrderInfo,
      tips = {},
      remarkInfo,
      isFulfillOrder,
      reload,
      orderExpressInfo,
      paymentInfo,
      sourceInfo = {},
      operations = [],
      qttOrderInfo = {}
    } = this.props;

    const stateDesc = isFulfillOrder
      ? get(tips, 'fulfillStateTips.fulfillState')
      : get(tips, 'orderDetailStateTips.orderStateDesc');

    const stateInfo = isFulfillOrder
      ? get(tips, 'fulfillStateTips.fulfillStateInfo')
      : get(tips, 'orderDetailStateTips.orderStateInfo');

    const isShowYzSecured = get(deepJsonParse(mainOrderInfo.tagsInfo), 'YZ_GUARANTEE', false);

    const autoCallInfo = get(orderExpressInfo, 'autoCallInfo', {} as IAutoCallInfo);
    const { isShow, autoDeliveryTime, autoDeliveryDesc } = autoCallInfo;
    const now = new Date();

    const showLearnMore = operations.find((item: IOperation) => item.code === 'learn_more');

    // 是否展示阶段支付信息（预售订单、非取消状态 & 有这个字段）
    // 2020-12-18 订单关闭状态又要展示了
    // ![OrderStateType.Cancel].includes(mainOrderInfo.state)

    const isShowPhasePayDetail =
      ORDER_TYPE.PRE_SALE.value === mainOrderInfo.orderType && paymentInfo?.phasePayDetailList;

    const extra = get(mainOrderInfo, 'extra', {});

    return (
      <div className={cx(style['order-state'], 'info-block')}>
        <Grid>
          <Row>
            <Col
              span={isFulfillOrder ? 24 : 8}
              className={cx('state-info-actions', {
                flex: isFulfillOrder
              })}
            >
              <div>
                <h2 className="state-desc">
                  {stateDesc}
                  {isShowYzSecured && !isFulfillOrder && (
                    <span className="yz-secured">
                      <img
                        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                        src="//b.yzcdn.cn/public_files/1f9ec96f0fa9c5e5e141eebf831c3bd4.png"
                        alt=""
                      />
                    </span>
                  )}
                </h2>
                <p className="grey">
                  {StateInfoRenderer(stateInfo as string)}
                  {showLearnMore && (
                    <BlankLink href={setUrlDomain('/displaylist/detail_4_4-1-28970', 'help')}>
                      了解更多
                    </BlankLink>
                  )}
                </p>
                {SupportParallelCallInLocalDelivery && (
                  <div className="state-autoCall">{autoDeliveryDesc}</div>
                )}
                {!SupportParallelCallInLocalDelivery &&
                  isShow &&
                  autoDeliveryTime &&
                  autoDeliveryTime - +now > 0 && (
                    <div className="state-autoCall">
                      {`已开启自动呼叫，系统将在 ${formatDate(
                        autoDeliveryTime,
                        'YYYY-MM-DD HH:mm'
                      )} 自动呼叫配送员`}
                    </div>
                  )}
                {/* 已发货且是微信管控订单且不是分店 */}
                {get(mainOrderInfo, 'state', '') === OrderStateType.Send &&
                  String(get(extra, 'aTTR_WX_SC_ORDER', 'false')) === 'true' && <WxControlNotice />}
              </div>
              {/* eslint-disable-next-line react/jsx-props-no-spreading */}
              <Actions {...this.props} isFulfillOrder={isFulfillOrder} reload={reload} />
            </Col>
            {!this.props.hideSteps && (
              <Col span={16} className="state-steps">
                {/* eslint-disable-next-line react/jsx-props-no-spreading */}
                <Steps {...(tips as any)} mainOrderInfo={mainOrderInfo} sourceInfo={sourceInfo} />
                {isShowPhasePayDetail ? <Phase data={paymentInfo.phasePayDetailList} /> : null}
              </Col>
            )}
          </Row>
        </Grid>
        <Tips tips={tips} remarkInfo={remarkInfo} qttOrderInfo={qttOrderInfo} />
      </div>
    );
  }
}

export default OrderState;
