import type { IOrderInfo } from 'definition/order-info';

import * as React from 'react';
import { get } from 'lodash';
import cx from 'classnames';
import { Tabs, Dialog, Button } from 'zent';
import { SuperButton } from '@youzan/react-components';
import { dataWatcher } from 'route/order-detail/data-watcher';

import createOrderCpm from 'components/create-order-cpm';
import { ExpressType } from 'common/constants/common';
import { HistoryRecords } from '../history-records';

import style from './style.scss';
import PackInfoItem from './pack-info';

const { TabPanel } = Tabs;

const { openDialog, closeDialog } = Dialog;
const DialogId = 'dialogId';

@createOrderCpm()
class PackInfo extends React.Component<IOrderInfo> {
  static defaultProps = {
    orderExpressInfo: {},
    itemInfo: [],
    mainOrderInfo: {},
    orderAddressInfo: {}
  };

  // renderTime = 0
  state = {
    activePackId: get(this.props, 'orderExpressInfo.packs[0].packId')
  };

  static getDerivedStateFromProps(
    nextProps: IOrderInfo,
    prevState: IOrderInfo
  ): { activePackId: string } | null {
    const activePackId: string = get(nextProps, 'orderExpressInfo.packs[0].packId');
    if (!prevState.activePackId && activePackId) {
      return { activePackId };
    }
    return null;
  }

  handlePackTabChange = (packId: string): void => {
    this.setState({
      activePackId: packId
    });
    dataWatcher.update({
      activePackId: packId
    });
  };

  render() {
    const { packs = [] } = this.props.orderExpressInfo;
    const { hideNav, mainOrderInfo, fulfillOrder } = this.props;
    const { orderNo } = mainOrderInfo || {};
    const { fulfillNo = '' } = fulfillOrder || {};
    return (
      <div className={cx(style.packs, 'info-block')}>
        {packs.length > 0 && (
          <Tabs
            activeId={this.state.activePackId}
            onChange={this.handlePackTabChange}
            className={cx({
              'hide-nav': hideNav
            })}
            type="card"
            overflowMode="slide"
            navExtraContent={
              mainOrderInfo.expressType === ExpressType.City && (
                <SuperButton
                  style={{ whiteSpace: 'nowrap' }}
                  type="link"
                  onClick={() => {
                    openDialog({
                      dialogId: DialogId,
                      title: '历史呼叫记录',
                      children: <HistoryRecords orderNo={orderNo} fulfillNo={fulfillNo} />,
                      style: {
                        width: '848px'
                      },
                      footer: (
                        <Button type="primary" onClick={() => closeDialog(DialogId)}>
                          关闭
                        </Button>
                      )
                    });
                  }}
                >
                  查看历史呼叫记录
                </SuperButton>
              )
            }
          >
            {packs
              .sort((a, b) => +a.packId - +b.packId)
              .map((packInfo, index) => {
                const displayPackNo = index + 1;
                const tabTitle = <span>{`包裹${displayPackNo}`}</span>;
                return (
                  <TabPanel key={packInfo.packId} id={packInfo.packId} tab={tabTitle}>
                    {/* eslint-disable-next-line react/jsx-props-no-spreading */}
                    <PackInfoItem key={packInfo.packId} {...this.props} packInfo={packInfo} />
                  </TabPanel>
                );
              })}
          </Tabs>
        )}
      </div>
    );
  }
}

export default PackInfo;
