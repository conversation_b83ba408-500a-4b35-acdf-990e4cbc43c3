import * as React from 'react';
import cx from 'classnames';
import { Dialog, ClampLines } from 'zent';
import { isEmpty, get, isNil, filter, isNumber, merge } from 'lodash';
import { BlankLink } from '@youzan/react-components';
import { deepJsonParse, formatDatetime, setUrlDomain } from '@youzan/retail-utils';
import {
  isRetailSingleStore,
  isUnifiedShop,
  isRetailMinimalistShop,
  isFrontWarehouse,
  isUnifiedOfflineBranchStore
} from '@youzan/utils-shop';

import type { Contains } from 'definition/common';
import {
  IBuyerInfo,
  IBuyerInvoiceInfo,
  IMainOrderInfo,
  IOrderAddressInfo,
  IOrderExchangeInfo,
  ITransactionReversalInfo
} from 'definition/order-info';
import { orderStateValue } from 'common/constants/models';
import { convertFenToYen } from 'common/fns/format';
import { customerDetailURL } from 'common/url';
import { ChannelType, OrderPayWayType, OrderStateType, ExpressType } from 'common/constants/common';
import { ORDER_TYPE } from 'common/constants/order';
import { insert } from 'common/utils';
import { withOrderHelper } from 'common/helper';
import {
  isCandaoOrder,
  isGYYOrder,
  checkIsElemeOrderByChannelType,
  checkIsMeituanPlatformOrder
} from 'common/biz-helper';
import { CloudSlot } from '@youzan/ranta-cloud-react';

import { styleUtils } from 'components/user-privacy';
import MarkRelationPerson from 'components/mark-relation-person';
import { UserPrivacyViewButtonWithData } from 'components/user-privacy';
import { checkAccess } from '@youzan/sam-components';
import createOrderCpm from '../../create-order-cpm';
import ModifyReceiverInfo from './modify-receiver-info';
import QueryAddressHistory from './query-address-history';
import Copy from './copy';

import style from './style.scss';

const { openDialog } = Dialog;
const ID_CARD_DIALOG = 'idCardDialog';

const OFFLINE = ChannelType.Offline;
const ONLINE = ChannelType.Online;

const GET_RECEIVE_INFO = 'getReceiverInfo';
const GET_EXPRESS_INFO = 'getExpressInfo';
const GET_PAYMENT_INFO = 'getPaymentInfo';
const GET_BUYER_INFO = 'getBuyerInfo';
const GET_WAREHOUSE_INFO = 'getWarehouseInfo';
const GET_CASHIER_INFO = 'getCashierInfo';
const GET_SHOPGUIDE_INFO = 'getShopGuideInfo';
const GET_CREDIT_INFO = 'getCreditInfo';
const BUYER_INVOICE_INFO = 'BUYER_INVOICE_INFO';
const INVOICE_WITH_MEMO = 'getInvoiceWithBuyerMemo';
const GET_EXCHANGE_INFO = 'getExchangeInfo';
const TRANSACTION_REVERSAL_INFO = 'transactionReversalInfo';

const ELECTRONIC_INVOICE = 1;
const VAT_SPECIAL_INVOICE = 2;
const invoiceTypeMap: any = {
  [ELECTRONIC_INVOICE]: '普通发票',
  [VAT_SPECIAL_INVOICE]: '增值税专用发票'
};

const ADDRESS = '收货地址';

const QRCODE_SCENE = 12; // 导购扫码场景值

const commonOnlineInfos = [
  {
    methodsName: GET_RECEIVE_INFO,
    title: '收货人信息'
  },
  {
    methodsName: GET_EXPRESS_INFO,
    title: '配送信息'
  }
];

type IOrderInfoContent = Array<{
  text: string;
  label: string;
}>;

type OrderInfoConfig = Record<string, any> & {
  methodsName: string;
  title: React.ReactNode;
  minWidth?: string;
};

type OrderInfoDisplayConfig = OrderInfoConfig & {
  display: boolean;
  actions: React.ReactNode[];
  content: IOrderInfoContent;
};

// 不同销售渠道订单显示信息不同
const showTypeOrderInfoMap = ({
  orderType,
  qrCodeScene
}: {
  orderType: number;
  qrCodeScene: number;
}) =>
  ({
    [OFFLINE]: [
      {
        methodsName: GET_PAYMENT_INFO,
        title: '付款信息'
      },
      {
        methodsName: GET_BUYER_INFO,
        title: '买家信息'
      },
      qrCodeScene === QRCODE_SCENE && orderType === ORDER_TYPE.QRCODE.value // 导购扫码场景值12，订单类型扫码订单
        ? {
            methodsName: GET_SHOPGUIDE_INFO,
            title: '导购员信息'
          }
        : {
            methodsName: GET_CASHIER_INFO,
            title: '收银员信息'
          }
    ],
    [ONLINE]: [
      ...commonOnlineInfos,
      {
        methodsName: GET_PAYMENT_INFO,
        title: '付款信息'
      },
      {
        methodsName: GET_BUYER_INFO,
        title: '买家信息'
      }
    ]
  } as Record<
    ChannelType,
    Array<{
      methodsName: string;
      title: React.ReactNode;
      minWidth?: string;
    }>
  >);

export type OrderInfoProps = Contains<{
  buyerInvoiceInfo?: IBuyerInvoiceInfo;
  mainOrderInfo: IMainOrderInfo;
  exchangeInfo: IOrderExchangeInfo;
  transactionReversalInfo?: ITransactionReversalInfo;
  orderAddressInfo: IOrderAddressInfo;
  itemInfo?: Array<
    Contains<{
      distTimeDimStr?: string;
      distTimeModeStr?: string;
    }>
  >;
  paymentInfo: Contains<{
    realPay: number;
    payment: number;
    fxPay?: number;
  }>;
  partPayDetail?: Contains<{
    partPays?: Array<
      Contains<{
        name: string;
        realPay: number;
        payWayDesc: string;
        payTime: number;
      }>
    >;
    remainFee: number;
  }>;
  buyerInfo: IBuyerInfo;
  remarkInfo: Contains<{
    buyerMemoDesc: string;
    giverPhone: number;
    greeting: string;
  }>;
  fulfillOrder: Contains<{
    warehouseName?: string;
  }>;
  regularCustomerInfo?: Contains<{
    contactName: string;
    contactPhone: string;
    enterpriseName: string;
  }>;
  encryptStr?: string;
  isGiftOrder(): boolean;
  isHotelOrder(): boolean;
  isSelfFetch(): boolean;
  isVirtualTicket(): boolean;
  isVirtualOrder(): boolean;
  isPeriodOrder(): boolean;
  isFenXiao(): boolean;
  isFreeGo(): boolean;
  reload: () => void;
}>;

interface OrderInfoState {
  decryptedData: {
    buyerInfo?: IBuyerInfo;
    orderAddressInfo?: IOrderAddressInfo;
    buyerInvoiceInfo?: IBuyerInvoiceInfo;
  };
  invoiceShowMore: boolean;
}

@createOrderCpm()
@withOrderHelper
class OrderInfo extends React.Component<OrderInfoProps, OrderInfoState> {
  constructor(props: OrderInfoProps) {
    super(props);
    this.state = {
      decryptedData: {},
      invoiceShowMore: false
    };
  }

  /**
   * 获取实际订单数据:
   * - 解密前, 展示脱敏数据
   * - 解密后, 展示展示数据
   */
  getFinalOrderInfoData = () => {
    return merge(
      {},
      {
        buyerInfo: this.props.buyerInfo,
        orderAddressInfo: this.props.orderAddressInfo,
        buyerInvoiceInfo: this.props.buyerInvoiceInfo
      },
      this.state.decryptedData
    );
  };

  // 收银员信息
  [BUYER_INVOICE_INFO] = () => {
    const { encryptStr } = this.props;
    const { invoiceShowMore } = this.state;

    const {
      buyerInvoiceInfo: {
        invoiceTitle,
        taxId = '-',
        receivingMailbox = '-',
        openingBankName = '-',
        bankAccount = '-',
        address = '-',
        phone = '-',
        invoiceType = ''
      } = {}
    } = this.getFinalOrderInfoData();

    const result = [];

    if (invoiceTitle) {
      result.push(
        this.formatContent(
          '发票抬头',
          <>
            {invoiceTitle}
            <UserPrivacyViewButtonWithData
              className={styleUtils.privacyButtonGap}
              encryptKey={encryptStr}
              onDecrypt={data => {
                this.setState(prevState => ({
                  decryptedData: {
                    ...prevState.decryptedData,
                    buyerInvoiceInfo: data.buyerInvoiceInfo
                  }
                }));
              }}
            />
          </>
        )
      );
    }

    const Items = [
      ['企业税号', taxId],
      ['收票邮箱', receivingMailbox],
      ['发票类型', invoiceTypeMap[invoiceType]],
      ['开户银行', openingBankName],
      ['银行卡号', bankAccount],
      ['联系方式', phone],
      ['地址', address]
    ];

    for (const item of Items) {
      const [name, val] = item;

      if (result.length === 3) {
        result.push(
          !invoiceShowMore &&
            this.formatContent(
              '',
              <div
                className="invoice-more-btn"
                onClick={() => this.setState({ invoiceShowMore: true })}
              >
                更多
              </div>
            )
        );
      }

      if (val && val !== '-') {
        if (result.length > 3) {
          result.push(invoiceShowMore && this.formatContent(name, val));
        } else {
          result.push(this.formatContent(name, val));
        }
      }
    }

    result.push(
      invoiceShowMore &&
        this.formatContent(
          '',
          <div
            className="invoice-more-btn"
            onClick={() => this.setState({ invoiceShowMore: false })}
          >
            收起
          </div>
        )
    );

    return result;
  };

  // 发票与备注
  [INVOICE_WITH_MEMO] = () => {
    const {
      remarkInfo: { buyerMemoDesc, giverPhone, greeting }
    } = this.props;

    const { buyerInvoiceInfo: { invoiceTitle, taxId } = {} } = this.getFinalOrderInfoData();

    const memoInfoBlock = [];

    if (invoiceTitle) memoInfoBlock.push(this.formatContent('发票', invoiceTitle));
    if (taxId) memoInfoBlock.push(this.formatContent('税号', taxId));
    memoInfoBlock.push(this.formatContent('备注', buyerMemoDesc));
    // 饿了么订单 增加订购人信息与祝福语
    if (giverPhone) memoInfoBlock.push(this.formatContent('订购人', giverPhone));
    if (greeting) memoInfoBlock.push(this.formatContent('祝福语', greeting));

    return memoInfoBlock;
  };

  [GET_CASHIER_INFO] = () => {
    const {
      mainOrderInfo: { cashierId = '-', cashierName = '-' }
    } = this.props;
    return [
      this.formatContent('收银员编号', cashierId),
      this.formatContent('收银员名称', cashierName)
    ];
  };

  [GET_EXCHANGE_INFO] = () => {
    const {
      exchangeInfo: { explanation = '-', reason = '-' }
    } = this.props;
    return [this.formatContent('换货原因', reason), this.formatContent('换货说明', explanation)];
  };

  [GET_SHOPGUIDE_INFO] = () => {
    const { mainOrderInfo } = this.props;
    try {
      const { name, phone } = deepJsonParse(
        deepJsonParse(get(mainOrderInfo, 'extraInfo', '')).DAOGOU
      );
      return [this.formatContent('导购员姓名', name), this.formatContent('导购员手机号', phone)];
    } catch (err) {
      return [this.formatContent('导购员姓名', '-'), this.formatContent('导购员手机号', '-')];
    }
  };

  [GET_CREDIT_INFO] = () => {
    const { regularCustomerInfo } = this.props;

    if (!regularCustomerInfo) {
      return [];
    }

    return regularCustomerInfo.enterpriseName
      ? [
          this.formatContent('挂账客户', regularCustomerInfo.enterpriseName),
          this.formatContent('联系人', regularCustomerInfo.contactName),
          this.formatContent('联系电话', regularCustomerInfo.contactPhone)
        ]
      : [
          this.formatContent('挂账客户', regularCustomerInfo.contactName),
          this.formatContent('联系电话', regularCustomerInfo.contactPhone)
        ];
  };

  // 收货人信息
  [GET_RECEIVE_INFO] = () => {
    const { mainOrderInfo } = this.props;

    const { orderAddressInfo } = this.getFinalOrderInfoData();

    // 送礼订单啥都不显示
    if (this.props.isGiftOrder()) {
      return [this.formatContent('', '送礼订单请查看礼单')];
    }

    const {
      receiverName = '',
      receiverTel = '',
      addressExtra = '',
      deliveryProvince = '',
      deliveryCity = '',
      deliveryDistrict = '',
      deliveryStreet = '',
      privacyRecipientPhone = '',
      backupRecipientPhone = ''
    } = orderAddressInfo;

    const extraInfo = deepJsonParse(addressExtra);

    // 公共信息(电话，身份证号)
    const commonInfos = [this.formatContent('联系电话', receiverTel)];

    const idCardInfo = get(deepJsonParse(mainOrderInfo.extraInfo, true), 'IDENTITY_CARD', {});
    let displayIdCardInfo = idCardInfo.idCardNumber || extraInfo.idCardNumber;

    if (idCardInfo.idCardFrontPhoto || idCardInfo.idCardBackPhoto) {
      displayIdCardInfo = (
        <span className="id-card-info">
          {displayIdCardInfo}
          <a className="id-card-info__show-btn" onClick={() => this.showIdCardPhotos(idCardInfo)}>
            [查看身份证]
          </a>
        </span>
      );
    }

    if (displayIdCardInfo) {
      commonInfos.push(this.formatContent('身份证号', displayIdCardInfo));
    }

    if (
      checkIsMeituanPlatformOrder(mainOrderInfo.channelType) ||
      checkIsElemeOrderByChannelType(mainOrderInfo.channelType)
    ) {
      privacyRecipientPhone &&
        commonInfos.push(this.formatContent('隐私号码', privacyRecipientPhone.replace('_', '转')));
      backupRecipientPhone &&
        commonInfos.push(this.formatContent('备用号码', backupRecipientPhone.replace('_', '转')));
    }

    // 酒店订单处理
    if (this.props.isHotelOrder()) {
      if (isEmpty(extraInfo)) {
        return commonInfos;
      }

      const { recipients = [], checkInTime = '-' } = extraInfo;
      return [
        ...recipients.map((name: any, index: number) =>
          this.formatContent(`入住人${index + 1}`, name)
        ),
        ...commonInfos,
        this.formatContent('入住时间：', checkInTime)
      ];
    }

    // 自提订单receiverName显示提货人
    if (this.props.isSelfFetch()) {
      return [this.formatContent('提货人', receiverName), ...commonInfos];
    }

    // 电子卡券receiverName显示联系人
    if (this.props.isVirtualTicket()) {
      return [this.formatContent('联系人', receiverName), ...commonInfos];
    }

    const addressDesc = filter(
      [deliveryProvince, deliveryCity, deliveryDistrict, deliveryStreet],
      desc => !isNil(desc) && desc !== ''
    );

    return [
      this.formatContent('收货人姓名', receiverName),
      ...commonInfos,
      this.formatContent(ADDRESS, addressDesc.join('-'))
    ];
  };

  // 配送信息
  [GET_EXPRESS_INFO] = () => {
    const { mainOrderInfo, orderAddressInfo, itemInfo = [] } = this.props;

    const { expressTypeDesc, expressTime } = mainOrderInfo;
    if (this.props.isVirtualTicket() || this.props.isHotelOrder() || this.props.isVirtualOrder()) {
      return [];
    }

    const formatExpressType = this.formatContent('配送方式', expressTypeDesc);

    if (this.props.isGiftOrder()) {
      return [formatExpressType];
    }

    const { expectDeliveryTimeDesc, selfFetchTime, selfFetchInfo } = orderAddressInfo;

    const {
      province = '',
      city = '',
      county = '',
      addressDetail = '',
      name: selfFetchName
    } = selfFetchInfo ?? {};

    if (this.props.isPeriodOrder()) {
      const { distTimeDimStr = '-', distTimeModeStr = '-' } = itemInfo[0];
      const expressContents: Array<{
        label: string;
        value: string | JSX.Element;
      }> = this.props.isSelfFetch()
        ? [
            { label: '配送方式', value: '到店自提' },
            { label: '提货次数', value: distTimeDimStr },
            { label: '提货日期', value: distTimeModeStr }
          ]
        : [
            { label: '配送次数', value: distTimeDimStr },
            { label: '送达日期', value: distTimeModeStr }
          ];
      this.props.isSelfFetch() &&
        selfFetchName &&
        selfFetchName !== '-' &&
        expressContents.push({
          label: '自提点地址',
          value: (
            <div className="store-shopping-address">
              <ClampLines
                lines={1}
                popWidth={320}
                text={[province, city, county, addressDetail].join('-')}
              />
            </div>
          )
        });
      return expressContents.map(contentItem =>
        this.formatContent(contentItem.label, contentItem.value)
      );
    }

    const formatExpressInfo = [formatExpressType];

    if (this.props.isSelfFetch()) {
      selfFetchTime && formatExpressInfo.push(this.formatContent('自提时间', selfFetchTime));
      selfFetchName && formatExpressInfo.push(this.formatContent('自提点', selfFetchName));
      selfFetchName &&
        selfFetchName !== '-' &&
        formatExpressInfo.push(
          this.formatContent(
            '自提点地址',
            <div className="store-shopping-address">
              <ClampLines
                lines={1}
                popWidth={320}
                text={[province, city, county, addressDetail].join('-')}
              />
            </div>
          )
        );
    } else {
      expressTime &&
        formatExpressInfo.push(this.formatContent('发货时间', formatDatetime(expressTime)));
    }

    if (expectDeliveryTimeDesc) {
      formatExpressInfo.push(this.formatContent('送达时间', expectDeliveryTimeDesc));
    }

    return formatExpressInfo;
  };

  // 付款信息
  [GET_PAYMENT_INFO] = () => {
    const {
      mainOrderInfo: { buyWay, buyWayDesc, state, payTime },
      paymentInfo: { realPay, payment }
    } = this.props;

    const textBuyWayMap: Record<number, string | undefined> = {
      [OrderPayWayType.GiftPay]: '礼品卡付款',
      [OrderPayWayType.CodPay]: '货到付款',
      [OrderPayWayType.CouponPay]: '优惠兑换',
      [OrderPayWayType.PresentPay]: '领取赠品'
    };

    // 代付款订单处理
    if (state === orderStateValue.TO_PAY) {
      return [this.formatContent('', '待付款')];
    }

    // 特殊付款方式显示
    if (textBuyWayMap[buyWay]) {
      return [this.formatContent('', textBuyWayMap[buyWay])];
    }

    // 代付订单处理
    if (buyWay === OrderPayWayType.PeerPay) {
      const { partPayDetail } = this.props;
      const partPays = partPayDetail?.partPays || [];
      const remainFee = partPayDetail?.remainFee ? (
        <span>{`剩余还有${convertFenToYen(partPayDetail.remainFee)}未支付`}</span>
      ) : (
        ''
      );

      let peerContents = [];
      remainFee && peerContents.push(this.formatContent('', remainFee));

      // 显示最后一个代付信息
      if (partPays.length > 0) {
        const { name, realPay: itemRealPay, payWayDesc, payTime: itemPayTime } = partPays[0] || {};
        peerContents = peerContents.concat([
          this.formatContent('代付人', name),
          this.formatContent(
            '实付金额',
            isNumber(itemRealPay) ? `¥${convertFenToYen(itemRealPay)}` : ''
          ),
          this.formatContent('支付方式', payWayDesc || ''),
          this.formatContent('支付时间', formatDatetime(itemPayTime))
        ]);
      }
      return peerContents;
    }

    return !isNumber(realPay)
      ? [this.formatContent('应付金额', payment ? `¥${convertFenToYen(payment)}` : '')]
      : [
          this.formatContent('实付金额', `¥${convertFenToYen(realPay)}`),
          this.formatContent('支付方式', buyWayDesc),
          this.formatContent('付款时间', formatDatetime(payTime))
        ];
  };

  // 买家信息
  [GET_BUYER_INFO] = () => {
    const {
      remarkInfo: { buyerMemoDesc },
      isFenXiao,
      mainOrderInfo,
      refundOrderInfo,
      encryptStr
    } = this.props;

    const {
      buyerInfo: { buyerId, buyerPhone, groupIsHeader, name }
    } = this.getFinalOrderInfoData();

    const displayName = groupIsHeader ? `${name || buyerPhone}(团长)` : name || buyerPhone;
    const displayBuyer =
      buyerId && displayName && !isFenXiao() && !isGYYOrder({ mainOrderInfo, refundOrderInfo }) ? (
        <BlankLink href={`${customerDetailURL}${buyerId}`}>{displayName}</BlankLink>
      ) : (
        displayName
      );

    const commonResults = [
      this.formatContent(
        '买家',
        <>
          {displayBuyer || '匿名用户'}
          <UserPrivacyViewButtonWithData
            className={styleUtils.privacyButtonGap}
            encryptKey={encryptStr}
            /** 老换货单可能没有买家信息, 需要隐藏隐私按钮 */
            isVisible={Boolean(displayBuyer)}
            onDecrypt={data => {
              this.setState(prevState => ({
                decryptedData: {
                  ...prevState.decryptedData,
                  buyerInfo: data.buyerInfo
                }
              }));
            }}
          />
        </>
      ),
      this.formatContent('买家留言', buyerMemoDesc)
    ];

    if (this.props.isFenXiao()) {
      const fenXiaoPay = this.props.paymentInfo?.fxPay ?? 0;
      commonResults.splice(
        1,
        0,
        this.formatContent('分销买家付款', `￥${convertFenToYen(fenXiaoPay)}`)
      );
    }

    const salesmanName = mainOrderInfo.salesmanInfo?.name;
    if (isUnifiedShop && salesmanName) {
      commonResults.push(this.formatContent('分销员', salesmanName));
    }

    return commonResults;
  };

  // 反结账信息
  [TRANSACTION_REVERSAL_INFO] = () => {
    const { reason, oriO, operatorName, refundSuccessTime } =
      this.props.transactionReversalInfo || {};

    if (oriO) {
      return [this.formatContent('反结账原单', oriO)];
    }
    return [
      this.formatContent('反结账时间', refundSuccessTime),
      this.formatContent('反结账原因', reason),
      this.formatContent('操作人', operatorName)
    ];
  };

  // 仓库信息
  [GET_WAREHOUSE_INFO] = () => {
    const { warehouseName = '' } = this.props.fulfillOrder;
    return [this.formatContent('', warehouseName)];
  };

  static defaultProps = {
    mainOrderInfo: {},
    orderAddressInfo: {},
    buyerInfo: {},
    remarkInfo: {},
    fulfillOrder: {},
    itemInfo: [],
    paymentInfo: {}
  };

  formatContent = (label?: string, text?: string | JSX.Element) => ({
    label: label ? `${label}：` : '',
    text: text || '-'
  });

  showIdCardPhotos = ({
    idCardFrontPhoto,
    idCardBackPhoto
  }: {
    idCardFrontPhoto: string;
    idCardBackPhoto: string;
  }) => {
    openDialog({
      dialogId: ID_CARD_DIALOG,
      title: '身份证照片',
      children: (
        <div className="dialog-content__id-photo">
          {[idCardFrontPhoto, idCardBackPhoto]
            .filter(img => !isNil(img))
            .map(url => (
              <figure key={url}>
                <img alt="身份证照片" width={600} src={url} />
              </figure>
            ))}
        </div>
      ),
      parentComponent: this
    });
  };

  canModifyReceiverInfo = () => {
    const {
      mainOrderInfo: { saleWay, orderType, expressType }
    } = this.props;

    /**
     * 支持修改订单:
     * 仅零售单店和连锁L 或 D版快递支持
     * 前置仓不支持
     * 门店下网店订单不支持
     * 送礼/酒店/周期购/社区团购订单不支持
     */
    return (
      (isRetailSingleStore ||
        isUnifiedShop ||
        (isRetailMinimalistShop && expressType === ExpressType.Express)) &&
      !isFrontWarehouse &&
      !(isUnifiedOfflineBranchStore && saleWay === ChannelType.Online) &&
      orderType !== ORDER_TYPE.GIFT.value &&
      orderType !== ORDER_TYPE.HOTEL.value &&
      orderType !== ORDER_TYPE.PERIOD.value &&
      orderType !== ORDER_TYPE.COMMUNITY_GROUPON.value &&
      checkAccess('修改订单')
    );
  };

  getInfoList = (
    orderInfoConfig: OrderInfoConfig[] = [],
    handlerByMethodName: Record<
      string,
      (config: OrderInfoConfig) => Partial<OrderInfoDisplayConfig>
    >
  ) => {
    return orderInfoConfig.map(config => {
      let display = true;
      let { title } = config;
      let after = null;
      const actions: React.ReactNode[] = [];
      const { methodsName } = config;
      const { encryptStr, isHotelOrder, isVirtualTicket, isVirtualOrder, reload } = this.props;
      // 这么恐怖的代码谁敢动啊，先 any 了
      const content: IOrderInfoContent | '' = (this as any)[methodsName]
        ? (this as any)[methodsName]()
        : '';

      // 收货人信息对于酒店订单和电子卡券订单是要单独处理的
      if (methodsName === GET_RECEIVE_INFO) {
        const { mainOrderInfo } = this.props;

        if (isHotelOrder()) {
          title = '入住人信息';
        }
        if (isVirtualTicket()) {
          title = '联系人信息';
        }

        let copyText = '';
        if (content) {
          const address = content.find(({ label }) => label.includes(ADDRESS))?.text ?? '';
          const contactsName = content.find(({ label }) => label.includes('姓名'))?.text ?? '';
          const contacts = content.find(({ label }) => label.includes('电话'))?.text ?? '';
          copyText = `${address} ${contactsName} ${contacts}`;

          actions.push(
            <Copy
              className="copy-button action__item"
              text={copyText}
              orderInfo={this.props.mainOrderInfo}
            >
              复制
            </Copy>
          );
        }

        const { isModifyLogistics, orderHistoryAddress } = this.props.orderAddressInfo || {};
        // 判断是否地址变更
        if (isModifyLogistics) {
          actions.push(
            <QueryAddressHistory
              className="action__item"
              orderNo={this.props.mainOrderInfo.orderNo as string}
              orderHistoryAddress={orderHistoryAddress}
            />
          );
        }

        if (this.canModifyReceiverInfo()) {
          actions.unshift(
            <ModifyReceiverInfo
              className="action__item"
              style={{ fontSize: '12px' }}
              fulfillNo={this.props.fulfillOrder.fulfillNo as string}
              expressType={this.props.mainOrderInfo.expressType as ExpressType}
              orderNo={this.props.mainOrderInfo.orderNo}
              reload={reload}
              orderState={this.props.mainOrderInfo.state as OrderStateType}
            />
          );
        }

        actions.unshift(
          <UserPrivacyViewButtonWithData
            className="action__item"
            encryptKey={encryptStr}
            onDecrypt={data => {
              this.setState(prevState => ({
                decryptedData: {
                  ...prevState.decryptedData,
                  orderAddressInfo: data.orderAddressInfo
                }
              }));
            }}
          />
        );

        after = (
          <MarkRelationPerson
            extraInfo={mainOrderInfo.extraInfo}
            orderNo={mainOrderInfo.orderNo}
            position="bottom-left"
            expressType={mainOrderInfo.expressType}
            newState={mainOrderInfo.newState}
          />
        );
      }

      if (methodsName === GET_EXPRESS_INFO && isVirtualOrder()) {
        display = false;
      }

      return {
        title,
        methodsName,
        actions,
        content,
        display,
        minWidth: config.minWidth || null,
        ...handlerByMethodName[methodsName]?.(config),
        after
      };
    });
  };

  render() {
    const { mainOrderInfo, isExpressOrder, transactionReversalInfo } = this.props;
    const isOfflinePreOrder = +(get(
      this.props,
      'mainOrderInfo.extra.iS_STORE_PRESALE_ORDER',
      0
    ) as number);
    const qrCodeScene = +(get(mainOrderInfo, 'extra.sCAN_QRCODE_SCENE', 0) as number);
    const isCandao = isCandaoOrder(this.props);
    const isMeituan = checkIsMeituanPlatformOrder(mainOrderInfo.channelType);
    const isEleme = checkIsElemeOrderByChannelType(mainOrderInfo.channelType);
    const { orderType } = mainOrderInfo;
    let showWay = mainOrderInfo.saleWay;
    /** 是否换货 */
    const isExchangeItem = !!get(this.props, 'exchangeInfo.exchangeNo');

    // 展示方式，虚拟订单和门店展示的信息一致，虚拟订单需要排除电子卡券的订单
    if (this.props.isVirtualOrder() && !this.props.isVirtualTicket()) {
      showWay = OFFLINE;
    }

    // 如果是扫码点单or门店预定,就是跟线上订单一样
    if (isOfflinePreOrder === 1 || this.props.isFreeGo()) {
      showWay = ONLINE;
    }

    let showOrderInfos = showTypeOrderInfoMap({ orderType, qrCodeScene })[showWay];

    if (this.props.regularCustomerInfo) {
      // 挂账信息
      const index = showOrderInfos.findIndex(item => item.methodsName === GET_BUYER_INFO);
      showOrderInfos = insert(
        showOrderInfos,
        // 插到 "买家信息" 后面, 如果找不到插最后
        index === -1 ? showOrderInfos.length : index + 1,
        {
          methodsName: GET_CREDIT_INFO,
          title: '挂账信息'
        }
      );
    }

    if (isOfflinePreOrder === 1) {
      showOrderInfos = [
        ...showOrderInfos,
        {
          methodsName: GET_CASHIER_INFO,
          title: '收银员信息'
        }
      ];
    }

    if (this.props.isVirtualOrder() && !this.props.isVirtualTicket()) {
      showOrderInfos = showOrderInfos.filter((ele: any) => ele.methodsName !== GET_CASHIER_INFO);
    }

    const { buyerInvoiceInfo, isShowTip } = this.props;

    if (buyerInvoiceInfo?.invoiceTitle) {
      showOrderInfos = [
        ...showOrderInfos,
        {
          title: (
            <>
              <span>发票信息</span>
              {isShowTip && (
                <a
                  href="//yingyong.youzan.com/cloud-app-detail/10003280"
                  rel="noopener noreferrer"
                  target="_blank"
                  style={{ marginLeft: 7 }}
                >
                  了解电子发票服务
                </a>
              )}
            </>
          ),
          methodsName: BUYER_INVOICE_INFO,
          minWidth: '190px'
        }
      ];
    }

    // 发货单详情页显示信息不一致
    if (isExpressOrder && showWay === ONLINE) {
      showOrderInfos = [
        ...commonOnlineInfos,
        {
          methodsName: GET_WAREHOUSE_INFO,
          title: '发货方信息'
        },
        {
          methodsName: GET_BUYER_INFO,
          title: '买家信息'
        }
      ];
    }

    // 餐道：收货人信息-》配送信息-》付款信息-》买家信息
    if (isCandao) {
      showOrderInfos = [
        {
          methodsName: GET_RECEIVE_INFO,
          title: '收货人信息'
        },
        {
          methodsName: GET_EXPRESS_INFO,
          title: '配送信息'
        },
        // 不展示收银员信息
        ...showOrderInfos.filter(ele => ele.methodsName !== GET_CASHIER_INFO)
      ];
    }

    // 美团订单 / 饿了么订单
    // 收货人信息 - 配送信息 - 付款信息 - 备注信息
    if (isMeituan || isEleme) {
      showOrderInfos = [
        {
          title: '收货人信息',
          methodsName: GET_RECEIVE_INFO
        },
        {
          title: '配送信息',
          methodsName: GET_EXPRESS_INFO
        },
        {
          title: '付款信息',
          methodsName: GET_PAYMENT_INFO
        },
        {
          title: '备注信息',
          methodsName: INVOICE_WITH_MEMO
        }
      ];
    }

    /** 网店换货不展示换货信息 */
    if (isExchangeItem && showWay === OFFLINE) {
      showOrderInfos = [
        ...showOrderInfos,
        {
          title: '换货信息',
          methodsName: GET_EXCHANGE_INFO
        }
      ];
    }

    // 反结账信息
    if (!isEmpty(transactionReversalInfo)) {
      showOrderInfos = [
        ...showOrderInfos,
        {
          title: '反结账信息',
          methodsName: TRANSACTION_REVERSAL_INFO
        }
      ];
    }

    const infoList = this.getInfoList(showOrderInfos, {
      [GET_PAYMENT_INFO]: () => {
        return { display: !isExchangeItem };
      }
    });

    const displayInfos = filter(infoList, { display: true });

    return (
      <ul className={cx('info-block', style['order-infos'])}>
        {displayInfos.map((info, index) => {
          let modStyle = {};
          if (info.minWidth) {
            modStyle = { minWidth: info.minWidth };
          }
          const content = (
            <li key={index} className="info-item" style={modStyle}>
              <h1 className="info-item__title">
                {info.title}
                {info.actions}
              </h1>
              {!!info.content &&
                info.content.map((contentInfo: { label: string; text: string }, i: number) => (
                  <p key={i}>
                    <span className="label">{contentInfo.label}</span>
                    <span className="text">{contentInfo.text}</span>
                  </p>
                ))}
              {info.after ? info.after : null}
            </li>
          );
          if (info.methodsName === GET_RECEIVE_INFO) {
            return <CloudSlot cloudKey="order-details-receiver">{content}</CloudSlot>;
          }
          return content;
        })}
      </ul>
    );
  }
}

export default OrderInfo;
