import React from 'react';
import { plus } from '@youzan/retail-utils';

import withOrderInfo from 'components/with-order-info';
import BasePayInfo from './base';

export function PaymentInfo({ paymentInfo = {}, itemInfo = [], normal, noRealPay }) {
  const { payment: orderPrice, postage, totalDiscount, paymentDescInfos } = paymentInfo;
  const goodsTotalPrice = itemInfo.reduce((pre, goods) => plus(pre, +goods.payPrice), 0);

  return (
    <BasePayInfo
      normal={normal}
      goodsTotalPrice={goodsTotalPrice}
      orderPrice={orderPrice}
      postage={postage}
      noRealPay={noRealPay}
      totalDiscount={totalDiscount}
      paymentDescInfos={paymentDescInfos}
    />
  );
}

export default withOrderInfo(PaymentInfo);
