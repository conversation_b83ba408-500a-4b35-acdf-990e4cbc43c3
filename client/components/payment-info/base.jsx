import React from 'react';
import styled from 'styled-components';

const StylePaymentInfo = styled.div`
  padding: ${({ normal }) => (normal ? '5px 5px' : '25px 40px')};
`;

const PriceLine = styled.div`
  display: flex;
  text-align: right;
  color: #646566;
  .price-label {
    flex: 1;
  }

  .price-value {
    width: ${({ normal }) => (normal ? 'auto' : '90px')};
  }
`;

const OrderPrice = styled(PriceLine)`
  margin-bottom: ${({ normal }) => (normal ? '5px' : '15px')};
  font-size: ${({ normal }) => (normal ? '12px' : '14px')};
  padding-top: ${({ normal }) => (normal ? '5px' : '40px')};

  .price-label {
    color: #000;
    font-weight: ${({ normal }) => (normal ? 'unset' : 'bold')};
  }

  .price-value {
    color: ${({ normal }) => (normal ? 'unset' : '#f44')};
  }
`;

function PaymentInfo(props) {
  const {
    goodsTotalPrice,
    postage,
    orderPrice,
    packingFee,
    totalDiscount,
    normal,
    noRealPay
  } = props;

  const priceLines = [
    ['商品总额', goodsTotalPrice],
    ['运费', postage],
    ['优惠', totalDiscount, true],
    ['配送费', packingFee],
    ['实付金额(元)', orderPrice]
  ];

  return (
    <StylePaymentInfo normal={normal}>
      {priceLines.map(([label, value, isReduce], idx) => {
        if ((noRealPay && idx === priceLines.length - 1) || !value) {
          return null;
        }

        const StyleLine = idx === priceLines.length - 1 ? OrderPrice : PriceLine;
        const money = `${isReduce ? '-' : ''}￥${(value / 100).toFixed(2)}`;

        return (
          <StyleLine key={label} normal={normal}>
            <span className="price-label">{label}：</span>
            <span className="price-value">{money}</span>
          </StyleLine>
        );
      })}
    </StylePaymentInfo>
  );
}

export default PaymentInfo;
