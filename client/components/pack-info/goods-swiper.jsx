import React from 'react';
import styled from 'styled-components';
import { global } from '@youzan/retail-utils';
import Swiper from 'components/swiper';
import { divGoodsNum } from 'common/helper';

const { SwiperItem } = Swiper;

const StyleGoodsSwiper = styled.div`
  display: flex;
  padding: 20px 0;

  .goods-item {
    color: #969799;
    width: 60px;
    margin-right: 10px;

    &__img {
      width: 60px;
      height: 60px;
    }

    &__text {
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
`;

const GoodsSwiper = ({ goodsList, packId }) => (
  <StyleGoodsSwiper>
    <Swiper uniqueId={packId}>
      {goodsList.map(goodsInfo => (
        <SwiperItem key={goodsInfo.itemIdStr}>
          <div className="goods-item">
            <img
              src={goodsInfo.imgUrl || global.DEFAULT_IMAGE}
              alt={goodsInfo.title}
              className="goods-item__img"
            />
            <p className="goods-item__text">{goodsInfo.title}</p>
            <p className="goods-item__text">
              数量：
              {divGoodsNum(goodsInfo.num)}
            </p>
          </div>
        </SwiperItem>
      ))}
    </Swiper>
  </StyleGoodsSwiper>
);

export default GoodsSwiper;
