import React from 'react';
import { find } from 'lodash';
import { formatDatetime } from '@youzan/retail-utils';

import withOrderInfo from '../with-order-info';

import BasePackInfo from './base.jsx';

const PackInfo = props => {
  const { itemInfo: goodsList = [], orderExpressInfo = {} } = props;
  const { packs = [] } = orderExpressInfo;
  if (packs.length === 0) {
    return null;
  }

  const data = packs.map(pack => {
    const {
      expressTypeDesc,
      extraInfo = {},
      expressTime,
      itemIds,
      packId,
      takeoutExpressDetail = {}
    } = pack;

    // TODO: 补充其他信息
    const baseInfos = [
      ['发货方式', expressTypeDesc],
      ['发货人', extraInfo.sentName],
      ['发货时间', formatDatetime(expressTime)]
    ];

    const itemInfo = itemIds.map(itemIdStr => find(goodsList, { itemIdStr }));

    // TODO: 区分同城配送和物流
    const stateDesc = takeoutExpressDetail.statusDesc || '无物流信息';
    const packLocus = takeoutExpressDetail.data || [];

    return {
      baseInfos,
      itemInfo,
      stateDesc,
      packLocus,
      packId
    };
  });

  return <BasePackInfo data={data} />;
};

export default withOrderInfo(PackInfo);
