import React from 'react';
import { formatDatetime } from '@youzan/retail-utils';
import styled from 'styled-components';

const StyleLocusSteps = styled.div`
  border-left: 1px solid #e5e5e5;
  margin-top: 20px;
  margin-right: 30px;
  flex: 2;

  .step-item {
    position: relative;
    margin-left: 10px;
    padding-top: 12px;
    padding-bottom: 12px;
    color: #646566;

    &:before {
      content: '';
      position: relative;
      display: block;
      top: 17px;
      right: 13px;
      width: 16px;
      height: 16px;
      background-color: #fff;
    }

    &:after {
      content: '';
      position: absolute;
      display: block;
      top: 33px;
      left: -14px;
      border-radius: 4px;
      width: 8px;
      height: 8px;
      background-color: #cacaca;
    }

    &:first-child {
      &:after {
        background-color: #f60;
      }

      & span:last-child {
        font-weight: bold;
      }
    }

    &:last-child {
      padding-bottom: 0;
    }

    &__desc > span {
      display: block;

      &:first-child {
        float: left;
        width: 150px;
        text-align: right;
      }

      &:last-child {
        margin-left: 180px;
      }
    }
  }
`;

const StyleLocus = styled.div`
  .state-title {
    margin-bottom: 6px;
    .orange {
      color: #f60;
    }
  }

  .steps-wrapper {
    height: 200px;
    padding-left: 6px;
    overflow-y: scroll;
  }
`;

function Locus(props) {
  const { stateDesc, packLocus } = props;

  const locus = packLocus.map((step, idx) => {
    const timeDesc = formatDatetime(step.time);
    return (
      <div key={idx} className="step-item">
        <p className="step-item__desc">
          <span>{timeDesc}</span>
          <span>{step.context}</span>
        </p>
      </div>
    );
  });

  return (
    <StyleLocus>
      <div className="state-title">
        物流状态： <span className="orange">{stateDesc}</span>
      </div>
      <div className="steps-wrapper">
        <StyleLocusSteps>{locus}</StyleLocusSteps>
      </div>
    </StyleLocus>
  );
}

export default Locus;
