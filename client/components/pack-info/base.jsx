import React from 'react';
import styled from 'styled-components';

import GoodsSwiper from './goods-swiper';
import Locus from './locus';
import withTab from '../with-tab';

const StylePackInfo = styled.div`
  border: ${({ withBorder }) => (withBorder ? '1px solid #f2f2f2' : 'none')};
  padding: 35px 15px 20px;
  display: flex;
  font-size: 12px;
  margin-bottom: 10px;
`;

const StyleBaseInfo = styled.div`
  flex: 1;
  max-width: 300px;
  padding: 0 20px;

  .info-line {
    margin-bottom: 5px;
  }
`;

function PackInfo({ data, withBorder = true }) {
  if (!data) {
    return null;
  }

  const { stateDesc = '', packLocus = [], baseInfos = [], itemInfo = [], packId } = data;

  const baseInfoEle = baseInfos.map(([label, value], idx) => (
    <p className="info-line" key={idx}>
      {label}：{value}
    </p>
  ));

  return (
    <StylePackInfo withBorder={withBorder}>
      <StyleBaseInfo>
        {baseInfoEle}
        <GoodsSwiper goodsList={itemInfo} packId={packId} />
      </StyleBaseInfo>
      <Locus stateDesc={stateDesc} packLocus={packLocus} />
    </StylePackInfo>
  );
}

export default withTab('包裹')(PackInfo);
