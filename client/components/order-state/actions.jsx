import React from 'react';
import { filter, get } from 'lodash';

import { DETAIL_REFUND_OPERATION_CODES } from 'components/opt-components/constant';
import { BUTTON, BUTTON_LINK } from 'components/opt-components/type-map';
import { OptComponent } from 'components/opt-components';

const createGroupOpts = operations => {
  // 过滤不显示的操作按钮
  const displayOpts = filter(operations, opt => !DETAIL_REFUND_OPERATION_CODES.includes(opt.code));

  const groupOpts = {
    buttonWithDesc: [],
    button: [],
    links: []
  };

  displayOpts.forEach((opt = {}) => {
    //  按钮组
    if ([BUTTON, BUTTON_LINK].includes(opt.type)) {
      // 文案的字段名称
      const descPropNames = ['goodsName', 'refundDesc'];

      // 有文案的 把文案放到buttonDesc组
      const itemDesc = descPropNames.reduce((pre, name) => {
        const value = get(opt, `attributes.${name}`);
        value && pre.push(value);
        return pre;
      }, []);

      const btnGroups = itemDesc.length > 0 ? groupOpts.buttonWithDesc : groupOpts.button;
      opt.desc = itemDesc;
      btnGroups.push(opt);
    } else {
      // link组
      groupOpts.links.push(opt);
    }
  });

  return groupOpts;
};

function Actions({ operations = [], options }) {
  const { buttonWithDesc, button, links } = createGroupOpts(operations);

  const getOptProps = (operation = {}) => ({
    options,
    operation
  });

  return (
    <div className="action-list">
      {buttonWithDesc.map((btnWithDesc, i) => (
        <div key={i} className="button-with-desc">
          <p className="gray refund-desc">{btnWithDesc.desc.join('   ')}</p>
          <OptComponent {...getOptProps(btnWithDesc)} />
        </div>
      ))}
      <div className="action-list__item">
        {button.map((buttonOpt, i) => (
          <OptComponent key={i} {...getOptProps(buttonOpt)} />
        ))}
      </div>
      <div className="actions-list__link">
        {links.map((linkOpt, i) => {
          // 钱款去向金额
          const { typeDesc, fee } = linkOpt.attributes || {};
          return (
            <p key={i} className="gray refund-desc">
              {typeDesc && `${typeDesc}：${fee}  `}
              <OptComponent {...getOptProps(linkOpt)} />
            </p>
          );
        })}
      </div>
    </div>
  );
}

export default Actions;
