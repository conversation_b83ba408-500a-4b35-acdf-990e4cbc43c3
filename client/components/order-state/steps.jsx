import React from 'react';
import { Steps } from 'zent';
import { findIndex, get } from 'lodash';
import { formatDatetime } from '@youzan/retail-utils';
import { isOuterOrder } from 'common/biz-helper';
import withOrderInfo from '../with-order-info';

const { Step } = Steps;

const TO_TUAN = {
  origin: 50,
  transform: 4.5
};

function StateSteps(props) {
  const isRefundOrder = !!props.refundOrderInfo;

  let state = get(props, 'refundOrderInfo.refundStateProgress');

  const isOuter = isOuterOrder(props);
  if (!isRefundOrder) {
    state = isOuter ? get(props, 'mainOrderInfo.newState') : get(props, 'mainOrderInfo.state');
  }

  const stateAssemblyList = isRefundOrder
    ? props.refundStateAssemblyList || []
    : get(props, 'tips.orderDetailStateTips.stateAssemblyList', []);

  if (stateAssemblyList.length === 0) {
    return <div className="order-state__steps" />;
  }

  // 订单状态需要对待成团特殊处理
  const compareState =
    !isRefundOrder && state === TO_TUAN.origin && !isOuter ? TO_TUAN.transform : state;
  const compareStateTips = stateAssemblyList.map(tip => {
    tip.compareState = !isRefundOrder && tip.code === TO_TUAN.origin ? TO_TUAN.transform : tip.code;
    return tip;
  });

  const currentIndex = findIndex(compareStateTips, item => item.compareState > compareState);

  // 对待成团处理下
  const stepsIndex = compareState === TO_TUAN.transform ? currentIndex + 1 : currentIndex;
  return (
    <div className="order-state__steps">
      <Steps current={stepsIndex === -1 ? compareStateTips.length : stepsIndex} status="finish">
        {compareStateTips.map((stepInfo, idx) => (
          <Step key={idx} title={stepInfo.content} description={formatDatetime(stepInfo.time)} />
        ))}
      </Steps>
    </div>
  );
}

export default withOrderInfo(StateSteps);
