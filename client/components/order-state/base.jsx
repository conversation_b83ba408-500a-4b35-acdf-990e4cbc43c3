import React from 'react';
import PropTypes from 'prop-types';
import LinkButton from 'components/link-button';
import styled from 'styled-components';

const StyleOrderState = styled.div`
  border: ${({ withBorder }) => (withBorder ? '1px solid #F2F2F2' : 'none')};
  padding: 30px 20px;

  .state-title {
    font-size: 20px;
    margin-bottom: 15px;
  }

  .state-content {
    color: #9b9b9b;
    line-height: 20px;
    font-size: 12px;
  }

  .order-actions {
    > div {
      margin-bottom: 10px;
    }
  }
`;

function OrderState(props) {
  const { onRemark, stateTitle, stateContent, className, actions, withBorder } = props;
  return (
    <StyleOrderState className={className} withBorder={withBorder}>
      <h2 className="state-title">{stateTitle}</h2>
      <div className="state-content">{stateContent}</div>
      {actions && <div className="order-actions">{actions}</div>}
      <div className="certain-actions">
        <LinkButton onClick={onRemark}>备注</LinkButton>
      </div>
    </StyleOrderState>
  );
}

OrderState.propTypes = {
  onRemark: PropTypes.func,
  className: PropTypes.string,
  stateTitle: PropTypes.string,
  stateContent: PropTypes.node,
  actions: PropTypes.node,
  withBorder: PropTypes.bool
};

export default OrderState;
