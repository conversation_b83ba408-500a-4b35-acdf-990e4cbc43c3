@import '~shared/style';

:local(.order-state) {
  display: flex;

  .order-state__info {
    flex: 1;
  }

  .order-state__steps {
    flex: 2;
    border: 1px solid $background-color-base;
    border-left: none;
    display: flex;
    align-items: center;
  }

  .action-list {
    display: flex;
    flex-direction: column;

    .button-with-desc,
    .refund-desc {
      margin-bottom: 10px;
    }

    &__item {
      margin-right: 15px;
      display: flex;
      margin-bottom: 10px;

      .refund-desc {
        min-width: 115px;
      }
    }
  }
}

:local(.bottom-divide) {
  &:last-child {
    margin-bottom: 10px;
  }

  &.reminder,
  &.remark {
    border: 1px solid $background-color-base;
    border-top: none;
  }
}
