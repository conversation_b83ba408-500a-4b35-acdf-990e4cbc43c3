import React from 'react';
import withOrderInfo from 'components/with-order-info';
import cx from 'classnames';
import { get } from 'lodash';
import Remark from 'components/opt-components/remark';
import LineContent from 'components/line-content';
import Reminder from 'components/reminder';

import BaseOrderState from './base';
import Actions from './actions';
import OrderSteps from './steps';

import style from './style.scss';

const getOrderStateProps = ({ fetchOrderInfo, operations = [], tips = {}, ...others }) => {
  const { orderStateInfo: stateContent, orderStateDesc: stateTitle } =
    tips.orderDetailStateTips || {};

  const options = {
    reload: fetchOrderInfo,
    orderInfo: {
      ...others,
      tips
    }
  };

  const actions = <Actions options={options} operations={operations} />;

  const remarkOptions = {
    reload: fetchOrderInfo,
    remark: get(others, 'remarkInfo.sellerMemoDesc'),
    orderNo: get(others, 'mainOrderInfo.orderNo')
  };

  const onRemark = () => Remark.openDialog({ options: remarkOptions });

  return {
    stateContent,
    stateTitle,
    withBorder: true,
    className: 'order-state__info',
    actions,
    onRemark
  };
};

export const BizOrderState = props => {
  const stateProps = getOrderStateProps(props);

  const { fetchOrderInfo, ...orderInfo } = props;
  const { buyerMemoDesc = '', sellerMemoDesc = '' } = get(orderInfo, 'remarkInfo', {});

  return (
    <div>
      <div className={cx(style['order-state'], style['bottom-divide'])}>
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        <BaseOrderState {...stateProps} />
        <OrderSteps />
      </div>
      <Reminder
        reload={fetchOrderInfo}
        orderInfo={orderInfo}
        className={cx(style['bottom-divide'], 'reminder')}
      />
      {(buyerMemoDesc || sellerMemoDesc) && (
        <LineContent
          label="买家留言"
          text={buyerMemoDesc}
          className={cx(style['bottom-divide'], 'remark')}
          right={
            <span className="gray">
              卖家备注：
              {sellerMemoDesc}
            </span>
          }
        />
      )}
    </div>
  );
};

export { BaseOrderState, OrderSteps };

export default withOrderInfo(BizOrderState);
