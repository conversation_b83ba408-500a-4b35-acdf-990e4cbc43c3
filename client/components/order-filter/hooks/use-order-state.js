import React, { useMemo } from 'react';
import { findKey, get, keys, findIndex, find } from 'lodash';
import { prePareStateTypeOptions } from '@youzan/zan-hasaki';

import { ORDER_TYPE } from 'common/constants/order';
import { OrderSourceType } from 'common/constants/common';

import { orderTypeValue } from 'common/constants/models';
import { orderStateLabelWithSaleWay, orderStateLabel } from 'common/constants/select-text';
import { OrderStateMultiSelectField } from 'components/fields-filter/rf';

const isWestcake = !!get(window, '_global.business.isWestcake');

// 当前只有幸福西饼商家会添加备货状态
if (isWestcake) {
  const { splice } = Array.prototype;

  keys(orderStateLabel).forEach(key => {
    const orderStates = orderStateLabel[key];
    const insertIndex = findIndex(orderStates, { value: 5 });

    if (!find(orderStates, { value: prePareStateTypeOptions[0].value })) {
      splice.apply(orderStates, [insertIndex, 0, ...prePareStateTypeOptions]);
    }
  });
}

const useOrderState = ({ fieldConfig, saleWay, allValues = {} }) => {
  const display = fieldConfig.orderState;

  const selectData = useMemo(() => {
    if (!display || (!allValues.orderType && allValues.orderType !== 0)) return [];

    // 订单类型
    const orderTypeKey = findKey(orderTypeValue, item => item === allValues.orderType);
    // 订单状态的筛选框文案与订单类型有关，而订单类型与销售渠道有关
    const orderStates = orderStateLabelWithSaleWay[saleWay][orderTypeKey];

    // 【来源】餐道订单且【类型】非拼团订单增加「待接单 TO_ACCEPT」状态
    if (
      [OrderSourceType.Eleme, OrderSourceType.Meituan].includes(allValues.orderSource) &&
      allValues.orderType !== ORDER_TYPE.TUAN.value &&
      allValues.orderType !== ORDER_TYPE.MEDICAL_ORDER.value
    ) {
      return orderStates.concat([{ text: '待接单', value: 50 }]);
    }

    return orderStates;
  }, [allValues.orderType, allValues.orderSource, display, saleWay]);

  console.log('selectData', selectData);

  const field = useMemo(
    () => (display ? <OrderStateMultiSelectField data={selectData} /> : null),
    [display, selectData]
  );

  return [field];
};

export default useOrderState;
