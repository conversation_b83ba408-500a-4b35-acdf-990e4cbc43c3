import React, { useEffect, useMemo } from 'react';
import { Field } from '@youzan/retail-form';
import { Select } from 'zent';
import './multi-select-rf.scss';

const ALL_OPTION = -1;

// 多选订单状态组件
const OrderStateMultiSelect = ({ input, data = [], width, className, disabled }) => {
  const { value = [], onChange } = input;

  console.log('value', value);

  // 检查是否包含全部选项
  const hasAllOption = Array.isArray(value) && value.includes(ALL_OPTION);

  // 生成自定义 className
  const customClassName = `order-state-multi-select ${className || ''} ${
    hasAllOption ? 'has-all-option' : ''
  }`.trim();

  // 将数据转换为 Zent Select 需要的格式
  const options = useMemo(() => {
    return data.map(item => ({
      key: item.value,
      text: item.text
    }));
  }, [data]);

  // 将 value 数组转换为 ISelectItem 数组
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(value)) return [];
    return value.map(val => {
      const option = options.find(opt => opt.key === val);
      return option || { key: val, text: String(val) };
    });
  }, [value, options]);

  // 处理选择变化
  const handleChange = selectedItems => {
    if (!Array.isArray(selectedItems)) {
      onChange([ALL_OPTION]); // 默认选择全部
      return;
    }

    const newValues = selectedItems.map(item => item.key);
    const oldValues = Array.isArray(value) ? value : [];

    // 找出新增的选项
    const addedValues = newValues.filter(val => !oldValues.includes(val));
    // 找出移除的选项（暂时保留，可能后续需要）
    // const removedValues = oldValues.filter(val => !newValues.includes(val));

    // 如果新增了"全部"选项，清空其他选项，只保留"全部"
    if (addedValues.includes(ALL_OPTION)) {
      onChange([ALL_OPTION]);
      return;
    }

    // 如果旧数据包含"全部"选项，且新增了其他选项，则移除"全部"选项
    if (
      oldValues.includes(ALL_OPTION) &&
      addedValues.length > 0 &&
      !addedValues.includes(ALL_OPTION)
    ) {
      const filteredValues = newValues.filter(val => val !== ALL_OPTION);
      onChange(filteredValues.length > 0 ? filteredValues : [ALL_OPTION]);
      return;
    }

    // 如果没有选择任何选项，默认选择"全部"
    if (newValues.length === 0) {
      onChange([ALL_OPTION]);
    } else {
      onChange(newValues);
    }
  };

  useEffect(() => {
    console.log('come 11', value);

    // 初始化为全部的时候
    if (value === ALL_OPTION) {
      onChange([ALL_OPTION]); // 默认选择全部
    }
  }, [onChange, value]);

  return (
    <Select
      value={selectedOptions}
      options={options}
      multiple
      placeholder="请选择订单状态"
      onChange={handleChange}
      width={width || 160}
      className={customClassName}
      disabled={disabled}
    />
  );
};

const OrderStateMultiSelectField = ({ data, width, className, disabled }) => (
  <div className="filter-item__field">
    <Field
      label="订单状态："
      name="orderStateList"
      component={OrderStateMultiSelect}
      type="rc"
      filter
      data={data}
      width={width}
      className={className}
      disabled={disabled}
    />
  </div>
);

export default OrderStateMultiSelectField;
