import React, { useMemo } from 'react';
import { Field } from '@youzan/retail-form';
import { Select } from 'zent';

// 多选订单状态组件
const OrderStateMultiSelect = ({ input, data = [], width, className, disabled }) => {
  const { value = [], onChange } = input;

  // 将数据转换为 Zent Select 需要的格式
  const options = useMemo(() => {
    return data.map(item => ({
      key: item.value,
      text: item.text
    }));
  }, [data]);

  // 将 value 数组转换为 ISelectItem 数组
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(value)) return [];
    return value.map(val => {
      const option = options.find(opt => opt.key === val);
      return option || { key: val, text: String(val) };
    });
  }, [value, options]);

  // 处理选择变化
  const handleChange = selectedItems => {
    console.log('selectedItems', selectedItems);

    if (!Array.isArray(selectedItems)) {
      onChange([-1]); // 默认选择全部
      return;
    }

    const values = selectedItems.map(item => item.key);

    // 如果选择了"全部"(-1)，清空其他选项，只保留"全部"
    if (values.includes(-1)) {
      onChange([-1]);
      return;
    }

    // 如果选择了其他选项，确保移除"全部"选项
    const filteredValues = values.filter(val => val !== -1);

    // 如果没有选择任何选项，默认选择"全部"
    if (filteredValues.length === 0) {
      onChange([-1]);
    } else {
      onChange(filteredValues);
    }
  };

  return (
    <Select
      value={selectedOptions}
      options={options}
      multiple
      placeholder="请选择订单状态"
      onChange={handleChange}
      width={width || 160}
      className={className}
      disabled={disabled}
    />
  );
};

const OrderStateMultiSelectField = ({ data, width, className, disabled }) => (
  <div className="filter-item__field">
    <Field
      label="订单状态："
      name="orderStateList"
      component={OrderStateMultiSelect}
      type="rc"
      filter
      data={data}
      width={width}
      className={className}
      disabled={disabled}
    />
  </div>
);

export default OrderStateMultiSelectField;
