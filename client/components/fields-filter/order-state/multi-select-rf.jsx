import React from 'react';
import { Field } from '@youzan/retail-form';
import { Select } from '@youzan/biz-select-center';

// 多选订单状态组件
const OrderStateMultiSelect = ({ input, data = [], ...props }) => {
  const { value = [], onChange } = input;

  return (
    <Select
      value={value}
      data={data}
      optionText="text"
      optionValue="value"
      multiple
      placeholder="请选择订单状态"
      onChange={onChange}
      {...props}
    />
  );
};

const OrderStateMultiSelectField = props => (
  <div className="filter-item__field">
    <Field
      label="订单状态："
      name="orderStateList"
      component={OrderStateMultiSelect}
      type="rc"
      filter
      {...props}
    />
  </div>
);

export default OrderStateMultiSelectField;
