import { Mode } from './react-components/business-hour';

export interface SelfFetchTimeRange {
  closeTime: string;
  openTime: string;
  dayCross: 0 | 1;
  weekdays: string[];
  timeRangeType: Mode;
}

/** 仓库类型 */
export enum WarehouseType {
  /** 1: 独立仓 */
  individual = 1,
  /** 2: 门店仓 */
  store = 2,
  /** 3: 虚拟仓 */
  virtual = 3,
  /** 4: 前置仓 */
  Front = 4,
  /** 5: 铺货下网店仓 */
  online = 5
}

/** 如果要选择退货地址，选择的来源 */
export enum ReturnAddressSelectSource {
  /** 商家地址库, 对应铺货模式 */
  AddressList = 1,
  /** 仓库、门店, 对应供货模式 */
  Warehouse = 2
}

export interface ISelfFetchCodesRes {
  qr_code: string;
  weapp_code: string;
}

export interface ISelfWriteOffReq {
  selfWriteOffType: 0 | 1;
  subKdtId: number;
}
