import type { IOrderInfo, IRetailSkuList } from 'definition/order-info';
import type { Contains } from 'definition/common';

import * as React from 'react';
import { repeat, isNil, entries, get, find, findIndex, isEmpty, keyBy, keys } from 'lodash';
import * as queryString from 'query-string';
import { DEFAULT_IMG } from 'common/constant';
import { times, deepJsonParse, div } from '@youzan/retail-utils';
import mapKeysToCamelCase from '@youzan/utils/string/mapKeysToCamelCase';
import { orderTypeValue } from 'common/constants/models';
import { ExpressType } from 'common/constants/common';
import { ChannelType, OrderGoodsType } from 'common/constants/common';
import { ORDER_TYPE } from './constants/order';

const { HOTEL, GIFT, POINT_STORE, FENXIAO, FREE_GO } = orderTypeValue;
const VIRTUAL_TICKET = OrderGoodsType.VirtualTicket;

const { SelfFetch } = ExpressType;
const ONLINE = ChannelType.Online;
const CALC_NUMBER = 1000;

/**
 * 检查是否为新的 分销订单
 */
export const checkIsNewFxOrder = (orderInfo: IOrderInfo): boolean =>
  JSON.parse(get(orderInfo, 'mainOrderInfo.extraInfo', '{}'))
    .IS_PURCHASE_NEW_SETTLEMENT_STRATEGY === 'true';

// 商品数量除以CALC_NUMBER
export const divGoodsNum = (num: number): number => div(num, CALC_NUMBER);

// 商品数量乘于CALC_NUMBER
export const timesGoodsNum = (num: number): number => times(num, CALC_NUMBER);

/**
 * switch 1024bytes to 1kb, 1024**2 bytes to 1mb
 * 文件的 size, 单位 bytes
 * 保留的小数点
 */
export const switchBytes = (b: string | number, precision = 2): string => {
  const bytes = +b || NaN;
  // 因为在 log 中, 指数不能为 0. 所以 0 bytes 的文件都做错误.
  if (Number.isNaN(bytes)) {
    return '文件大小不正常';
  }
  const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
  const { log, trunc } = Math;

  const value = trunc(log(bytes) / log(1024));
  return `${(bytes / 1024 ** value).toFixed(precision)} ${units[value]}`;
};

export const differenceOfObject = (
  obj: Record<string, unknown> = {},
  diffObj: Record<string, unknown> = {}
): Record<string, unknown> =>
  entries(diffObj).reduce<Record<string, unknown>>((preRes, [key, value]) => {
    if (obj[key] !== value) {
      preRes[key] = value;
    }
    return preRes;
  }, {});

/**
 * 下面这个逻辑有问题
 * ...(find(itemInfo, { itemIdStr: itemId }) || {}), 应该永远返回 {}, 因为 itemInfo 根本不存在 itemIdStr 字段
 */
export const pickItemsByItemIds = (
  _: unknown,
  itemInfo?: Array<Contains<{ itemIdStr: string }>>,
  distItemInfo?: Array<Contains<{ itemId: string; num: number }>>
): Array<Contains<{ num: number }>> => {
  return (
    distItemInfo?.map(({ itemId, num }) => {
      return {
        ...(find(itemInfo ?? [], { itemIdStr: itemId }) || {}),
        num: timesGoodsNum(num)
      };
    }) ?? []
  );
};

// 从商品信息里取出聚合后的优惠数据(相同活动的价格进行聚合)
export const getItemUmpInfo = (
  itemInfo: Array<
    Contains<{
      goodsActivities?: Array<Contains<{ activityName: string; decrease: number }>>;
      num?: number;
    }>
  > = [],
  mainOrderInfo: Contains<{ saleWay?: ChannelType }> = {}
): Array<Record<string, unknown>> => {
  const isOnline = mainOrderInfo.saleWay === ONLINE;
  const itemUmpInfo = itemInfo.reduce(
    (pre: Array<Contains<{ decrease: number; displayDecrease?: number }>>, current) => {
      current.goodsActivities?.forEach(itemActivite => {
        const shallowCopy = { ...itemActivite };
        const activeIndex = findIndex(pre, {
          activityName: shallowCopy.activityName
        });
        const itemDecrease = shallowCopy.decrease || 0;
        const itemNum = current.num || 0;
        // 网店的商品decrease需要 乘商品数量
        const sumDecrease = isOnline ? times(itemDecrease, divGoodsNum(itemNum)) : itemDecrease;
        shallowCopy.decrease = sumDecrease;
        if (activeIndex > -1) {
          pre[activeIndex].decrease += sumDecrease;
          pre[activeIndex].displayDecrease = pre[activeIndex].decrease;
        } else {
          pre.push(shallowCopy);
        }
      });
      return pre;
    },
    []
  );
  return itemUmpInfo;
};

// 订单级别的优惠信息聚合(按照activityName)
export const combineUmpInfo = (
  activities: Array<Contains<{ decrease: number; displayDecrease: number }> | any> = [],
  key = 'activityName'
): any[] => {
  // 这里改不动了, 先 any 了
  return activities.reduce(
    (pre: Array<Contains<{ decrease: number; displayDecrease: number }>>, activity) => {
      const activeIndex = findIndex(pre, { [key]: activity[key] });
      if (activeIndex > -1) {
        pre[activeIndex].displayDecrease += activity.decrease;
      } else {
        activity.displayDecrease = activity.decrease;
        pre.push(activity);
      }
      return pre;
    },
    []
  );
};

// 递归的删除掉所有值等于 all 和 全部 的属性
export const dropAll = (obj: Record<string, unknown>): Record<string, unknown> => {
  // 后端接口不支持这两种值
  const res: Record<string, unknown> = JSON.parse(JSON.stringify(obj));
  Object.keys(res).forEach(key => {
    if (Object.prototype.toString.call(res[key]) === '[object Object]') {
      res[key] = dropAll(res[key] as Record<string, unknown>);
    } else {
      res[key] === 'all' || res[key] === '全部' ? delete res[key] : null;
    }
  });
  return res;
};

// 兼容订单中商品图片url 前缀重复的问题
export function getCorrectImgUrl(originUrlParam = ''): string {
  // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
  const hostAppearTimes = originUrlParam.split('//img.yzcdn.cn').length - 1;
  let originUrl = originUrlParam;
  if (hostAppearTimes > 1) {
    // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
    const showCutStr = repeat('//img.yzcdn.cn/', hostAppearTimes - 1);
    originUrl = originUrlParam.replace(showCutStr, '');
  }
  return originUrl || DEFAULT_IMG;
}

// 过滤空字段
// 空字段四种情况： null和undefined , '', -1, [-1]
export function filterEmptyParams(
  originParams: Record<string, unknown> = {}
): Record<string, unknown> {
  const finalParams = entries(originParams).reduce((pre: Record<string, unknown>, [key, value]) => {
    if (value !== '' && !isNil(value) && value !== -1 && JSON.stringify(value) !== '[-1]') {
      pre[key] = value;
    }
    return pre;
  }, {});
  return finalParams;
}

//  业务使用  时间参数做格式转化
// filterData里面 时间返回的是一个字段，需要转化
// 下单时间 orderTime: [startTime, endTime]
// 同城配送 送达时间 deliveryTime: [deliveryStartTime, deliveryEndTime]
export const getOrderFormDataFromRealParams = ({
  startTime = '',
  endTime = '',
  deliveryStartTime = '',
  deliveryEndTime = '',
  orderState,
  orderStateList,
  ...otherQuery
}: {
  startTime?: string;
  endTime?: string;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  orderState?: any;
  orderStateList?: any[];
  [key: string]: any;
}): {
  orderTime: string[];
  deliveryTime: string[];
  orderStateList?: any[];
  [key: string]: any;
} => {
  const result: any = {
    orderTime: [startTime, endTime],
    deliveryTime: [deliveryStartTime, deliveryEndTime],
    ...otherQuery
  };

  // 处理订单状态：将单个状态或状态数组转换为数组格式
  if (orderStateList && Array.isArray(orderStateList)) {
    result.orderStateList = orderStateList;
  } else if (orderState !== undefined && orderState !== null && orderState !== '') {
    result.orderStateList = [orderState];
  }

  return result;
};

export const getRealParamsFromOrderFormData = ({
  orderTime = ['', ''],
  deliveryTime = ['', ''],
  selfFetchTime = ['', ''],
  orderStateList = [],
  ...otherParams
}: {
  orderTime?: string[];
  deliveryTime?: string[];
  selfFetchTime?: string[];
  orderStateList?: any[];
  [key: string]: any;
}): {
  startTime: string;
  endTime: string;
  deliveryStartTime: string;
  deliveryEndTime: string;
  selfFetchStartTime: string;
  selfFetchEndTime: string;
  orderState?: any;
  orderStateList?: any[];
  [key: string]: any;
} => {
  const result: any = {
    ...otherParams,
    startTime: orderTime[0],
    endTime: orderTime[1],
    deliveryStartTime: deliveryTime[0],
    deliveryEndTime: deliveryTime[1],
    selfFetchStartTime: selfFetchTime[0],
    selfFetchEndTime: selfFetchTime[1]
  };

  // 处理订单状态多选：
  // 1. 如果选择了"全部"(-1)，则不传递任何订单状态参数
  // 2. 如果选择了一个具体状态，传递 orderState 单值
  // 3. 如果选择了多个具体状态，传递 orderStateList 数组
  if (Array.isArray(orderStateList) && orderStateList.length > 0) {
    // 检查是否选择了"全部"
    if (orderStateList.includes(-1)) {
      // 选择了"全部"，不传递任何订单状态参数
      // 不设置 result.orderState 和 result.orderStateList
    } else {
      // 没有选择"全部"，传递具体的状态值
      if (orderStateList.length === 1) {
        result.orderState = orderStateList[0];
      } else {
        result.orderStateList = orderStateList;
      }
    }
  }

  return result;
};

export const isFalseValue = (value: string | number | boolean | null | undefined): boolean =>
  [null, undefined, 0, '', NaN, false].includes(value);

// 网店订单
export const isOnlineOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ saleWay: ChannelType }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.saleWay') === ONLINE;

// 是否虚拟卡券
export const isVirtualTicket = (
  orderInfo: Contains<{
    itemInfo?: Array<Contains<{ goodsType: OrderGoodsType }>>;
  }>
): boolean => get(orderInfo, 'itemInfo[0].goodsType') === VIRTUAL_TICKET;

// 是否酒店订单
export const isHotelOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ orderType: number }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.orderType') === HOTEL;

// 是否送礼订单
export const isGiftOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ orderType: number }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.orderType') === GIFT;

// 是否虚拟订单
export const isVirtualOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ tagsInfo?: Contains<{ IS_VIRTUAL?: number }> }>;
  }>
): boolean => {
  const tagInfo = deepJsonParse(get(orderInfo, 'mainOrderInfo.tagsInfo', '{}'));
  return isEmpty(tagInfo) ? false : tagInfo.IS_VIRTUAL;
};

// 是否是周期购
export const isPeriodOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ isPeriodBuy?: boolean }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.isPeriodBuy', false) as boolean;

// 是否是自提
export const isSelfFetch = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ expressType: ExpressType }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.expressType') === SelfFetch;

// 是否是积分兑换订单
export const isPoints = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ orderType: number }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.orderType') === POINT_STORE;

// 扫码点单订单
export const isFreeGo = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ orderType: number }>;
  }>
): boolean => get(orderInfo, 'mainOrderInfo.orderType') === FREE_GO;

// 判断是否是离线订单
export const isOfflineOrder = (
  orderInfo: Contains<{
    mainOrderInfo?: Contains<{ tagsInfo?: Contains<{ IS_OFFLINE_ORDER?: unknown }> }>;
  }>
): boolean => {
  const tagInfo = deepJsonParse(get(orderInfo, 'mainOrderInfo.tagsInfo', '{}'));
  return isEmpty(tagInfo) ? false : tagInfo.IS_OFFLINE_ORDER;
};

// 是否分销采购订单
export const isFenXiao = (orderInfo: IOrderInfo): boolean =>
  orderInfo?.mainOrderInfo?.orderType === FENXIAO;

export const orderHelper = {
  isVirtualTicket,
  isHotelOrder,
  isGiftOrder,
  isVirtualOrder,
  isPeriodOrder,
  isSelfFetch,
  isPoints,
  isOnlineOrder,
  isOfflineOrder,
  isFenXiao,
  isFreeGo
};

export function withOrderHelper<T>(WrapperCpm: T): T;
export function withOrderHelper<T extends {}>(WrapperCpm: React.ComponentClass<any>): any {
  type OrderHelperWithProps = Record<keyof typeof orderHelper, () => boolean>;

  function render(props: T) {
    const orderHelperWithProps: OrderHelperWithProps = entries(orderHelper).reduce(
      (newHelper, [name, fn]) => {
        newHelper[name] = () => fn({ ...props });
        return newHelper;
      },
      {} as Contains<{ [P in keyof typeof orderHelper]: () => boolean }>
    );

    // eslint-disable-next-line react/jsx-props-no-spreading
    return <WrapperCpm {...props} {...orderHelperWithProps} />;
  }

  return render;
}

export const baiduToGaode = (
  originLat: string | number,
  originLng: string | number
): {
  lng: number;
  lat: number;
} => {
  const lng = +originLng - 0.0065 || 0;
  const lat = +originLat - 0.006 || 0;

  return {
    lng: +Number(lng).toFixed(6),
    lat: +Number(lat).toFixed(6)
  };
};

/**
 * 根据不同类型返回配送方式的文案
 */
export function getExpressLabel(type: string | number): string {
  let expressWay;

  switch (+type) {
    case 0:
      expressWay = '快递';
      break;
    case 1:
      expressWay = '自提';
      break;
    case 2:
      expressWay = '同城配送';
      break;
    default:
      expressWay = '';
  }

  return expressWay;
}

/**
 * 获取商品明细相关数据
 *
 * 因为后端在接口里可能会存在 retailSkuList || relatedRetailSkuInfos 两个字段
 * 并且其中有一个可能是有错误的
 * 所以前端需要进行兼容处理
 */
export const getRelatedSkuList = (
  goodsInfo: Contains<{
    relatedRetailSkuInfos?: Partial<IRetailSkuList>;
    retailSkuList?: Partial<IRetailSkuList>;
  }>
): Partial<IRetailSkuList> => {
  const isRelatedRetailSkuInfoExist = !!(goodsInfo.relatedRetailSkuInfos || []).length;
  if (isRelatedRetailSkuInfoExist) {
    return goodsInfo.relatedRetailSkuInfos!;
  }

  return goodsInfo.retailSkuList || [];
};

/**
 * 动态注入新的 orderType
 * 配合 order-filter 使用
 * @from 零售 3.0 支持分销, 为了避免影响老店铺显示分销订单类型逻辑
 */
export function injectOrderTypes(originalTypes: any[], typeKeys: string[] = []): any {
  const typeKeysArr = !Array.isArray(typeKeys) ? [typeKeys] : typeKeys;
  const valueToOrderTypeObjMap = keyBy(originalTypes, 'value');

  // 插入新的 orderType 信息
  typeKeysArr.forEach((typeKey: keyof typeof ORDER_TYPE): void => {
    const item = ORDER_TYPE[typeKey];
    valueToOrderTypeObjMap[item.value] = item;
  });

  // 按顺序生成新的 orderTypes
  return keys(ORDER_TYPE)
    .map((typeKey: keyof typeof ORDER_TYPE) => {
      const { value } = ORDER_TYPE[typeKey];
      return valueToOrderTypeObjMap[value] || null;
    })
    .filter(v => v);
}

/**
 * 删除指定 orderType
 *
 * @from 零售 3.0 支持分销
 *
 * @export
 * @param {*} originalTypes - 老订单类型列表
 * @param {*} [typeKeys=[]] - 如 ['FENXIAO', 'FX_BUYER', xxx]
 * @returns
 */
export function removeOrderTypes(originalTypes: any[], typeKeys: string[] = []): any[] {
  const typeKeysArr = !Array.isArray(typeKeys) ? [typeKeys] : typeKeys;

  const filteredByValueMap = typeKeysArr.reduce(
    (res, typeKey: keyof typeof ORDER_TYPE): Record<string, boolean> => {
      const { value } = ORDER_TYPE[typeKey];
      res[value] = true;
      return res;
    },
    {} as Record<string, boolean>
  );

  return originalTypes.filter(({ value }) => !filteredByValueMap[value]);
}

/**
 * 获取拼装的 SKU 信息字符串
 */
export function getSkuText(specs: string, name = '', delimiter = ' '): string {
  let skuText = '';
  try {
    skuText = (JSON.parse(specs) as Array<{ v: string }>).map(spec => spec.v).join(delimiter);
    skuText = skuText.length > 0 ? skuText : name;
  } catch (err) {
    skuText = name;
  }

  return skuText;
}

/**
 * 拼装属性的信息字符串
 */
export function getPropertyText(
  properties: Array<{ [others: string]: unknown; valName: string }> = [],
  delimiter = ' '
): string {
  return `${properties.length > 0 ? '；' : ''}${properties
    .map(property => property.valName)
    .join(delimiter)}`;
}

/**
 * 从 window.location.hash 中获取 query string
 *
 * 原因：项目中，很多地方使用了 hash route，导致 query string 放在了 hash 之后，
 * 于是
 * > window.location.hash => '#/?a=xxx'
 * >
 * > window.location.search => ''
 *
 * 所以无法正确使用 queryString，于是用此函数去除多余的部分，便于 parse
 *
 * ⚠️ **会将 snake-case 转换为 camelCase** ⚠️
 */
export function getQueryFromHash(): Record<string, unknown>;
export function getQueryFromHash(type: 'string'): string;
export function getQueryFromHash(typeOrEmpty?: 'string'): any {
  const RemoveBeforeQueryString = /#.*?[^\?]/; // 用于 移除 query-string 之前多余的 字符
  const pureQuery = window.location.hash.replace(RemoveBeforeQueryString, '');

  if (typeOrEmpty === 'string') {
    return pureQuery;
  }

  return mapKeysToCamelCase(queryString.parse(pureQuery) ?? {}, true);
}

/**
 * 检查 query string，判断当前页面是否应该直接打开「发货弹窗」
 *
 * 当 URL 中 open_fulfill_dialog 为 truthy 时，返回 true，否则返回 false
 */
export const shouldOpenFulfillDialog = (): boolean => {
  const queryMap = getQueryFromHash();
  const { openFulfillDialog } = queryMap;
  return !!openFulfillDialog;
};

/**
 * 将元转化为分
 *
 * 若传入 falsify 类型值，会处理为 0
 */
export const convertYenToFen = (fen: number): number => {
  return +times(+fen || 0, 100);
};

/**
 * 取不到, 就用默认值,
 * 出于实际使用场景的考虑, 先把类型定义死为 string | number 了
 */
export function getMapWithDefault<T extends Map<string | number, string | number>>(
  map: T,
  key: string | number,
  defaultValue: string | number
): string | number {
  if (map.has(key)) {
    return map.get(key) as string | number;
  }
  return defaultValue;
}
