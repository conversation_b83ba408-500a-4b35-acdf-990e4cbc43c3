import React from 'react';
import { Button } from 'zent';
import {
  isBranchStore,
  isHqStore,
  isPartnerStore,
  isRetailMinimalistShop,
  isOfflineBranchStore,
  isSingleStore
} from '@youzan/utils-shop';

import * as constMap from 'common/constants';
import { TimeType, TimeTypeTextMap } from 'common/constants/order';
import { marketingTypeTextMap } from 'common/constants/common';
import cx from 'classnames';
import * as helper from '../helper';
import widthExportState from './withExportState';

// 定义新老报表，新报表包含订单商品两个维度，为了便于区分，将老报表定义为普通报表
const ExportItem = params => {
  const { buyWayOldMap, stateMap, expressTypeMap, feedbackMap, saleWayMap } = constMap;

  // const cashierName = helper.getCashierName(params);
  const orderTypeName = helper.getOrderType(params);
  const feedback = helper.getFeedbackKey(params);

  const {
    exportTime,
    nickName,
    userPhone,
    startTime,
    endTime,
    saleWay,
    orderNo,
    orderState,
    orderTags,
    expressType,
    buyWay,
    star,
    tel,
    outerTransactionNumber,
    userName,
    periodSendTime,
    orderLabel,
    update,
    goodsTitle,
    isLoading,
    shopName = '',
    isShowRefresh,
    renderExportState,
    buyerPhone,
    marketingType = -1,
    timeType = TimeType.BookTime
  } = params;
  const { printDefaultString, printDateBySeconds } = helper;

  // 报表维度，新报表要区分订单维度和报表维度，同时老报表就使用普通报表，old表示

  let displaySaleWay = saleWay ? saleWayMap[saleWay] : '网店';

  const cellStyle = (item, className) =>
    cx('export-item__cell', className, { 'export-item__hidden': !item });

  if (isHqStore || isPartnerStore) {
    displaySaleWay = shopName;
  }

  const orderStateText = orderState
    .split(',')
    .map(state => stateMap.all[state])
    .join('、');

  return (
    <li className="export-item">
      <p className="export-item__title">
        <span>
          报表申请时间：
          {printDateBySeconds(exportTime, 'YYYY-MM-DD HH:mm:ss')}
        </span>
        <span className="float-right">
          {`申请人：${printDefaultString(nickName)} ${printDefaultString(userPhone)}`}
        </span>
      </p>
      <div className="export-item__body">
        <div className="export-item__content">
          <div className="export-item__info">
            <span className={cellStyle(orderNo)}>{`订单编号：${printDefaultString(orderNo)}`}</span>
          </div>
          <div className="export-item__info">
            <span className={cellStyle(outerTransactionNumber)}>
              {`外部单号：${printDefaultString(outerTransactionNumber)}`}
            </span>
            <span className={cellStyle(userName)}>
              {`收货人姓名：${printDefaultString(userName)}`}
            </span>
            <span className={cellStyle(tel)}>{`收货人手机号：${printDefaultString(tel)}`}</span>
            <span className="export-item__cell" />
          </div>
          {orderLabel === 'offline_order_no' && (
            <div className="export-item__info">
              <span className={cellStyle(outerTransactionNumber)}>
                {`离线订单单号：${printDefaultString(outerTransactionNumber)}`}
              </span>
            </div>
          )}
          <div className="export-item__info">
            <span className={cellStyle(buyerPhone)}>
              {`买家手机号：${printDefaultString(buyerPhone)}`}
            </span>
            <span className={cellStyle(goodsTitle)}>
              {`商品名称：${printDefaultString(goodsTitle)}`}
            </span>
            <span className="export-item__cell" />
            <span className="export-item__cell" />
          </div>
          <div className="export-item__info">
            <span className="export-item__cell export-time">
              {`${TimeTypeTextMap[timeType]}：${printDateBySeconds(
                startTime,
                'YYYY-MM-DD HH:mm:ss'
              )} 至 ${printDateBySeconds(endTime, 'YYYY-MM-DD HH:mm:ss')}`}
            </span>
          </div>
          <div className="export-item__info">
            <span
              className={cellStyle(!isRetailMinimalistShop && !isBranchStore && displaySaleWay)}
            >
              {`销售渠道：${displaySaleWay}`}
            </span>
            <span className="export-item__cell">
              {`订单类型：${printDefaultString(orderTypeName)}`}
            </span>
            <span className="export-item__cell">
              {orderTags === 'IS_PRESCRIPTION_DRUG_ORDER' && orderState === 'totuan'
                ? '订单状态：审核中'
                : `订单状态：${printDefaultString(orderStateText)}`}
            </span>
            <span className="export-item__cell">
              {`退款状态：${printDefaultString(feedbackMap.all[feedback])}`}
            </span>
            {!isRetailMinimalistShop && (isBranchStore || !displaySaleWay) && (
              <span className="export-item__cell" />
            )}
          </div>
          <div className="export-item__info">
            <span className="export-item__cell">
              {`配送方式：${printDefaultString(expressTypeMap.all[expressType])}`}
            </span>
            <span className="export-item__cell">
              {`支付方式：${printDefaultString(buyWayOldMap[buyWay])}`}
            </span>
            <span className="export-item__cell">{`是否加星：${star ? '加星' : '不限'}`}</span>
            {!isRetailMinimalistShop && <span className="export-item__cell" />}
          </div>
          <div className="export-item__info">
            {/* <span className={cellStyle(cashierName)}>
              {`收银员：${printDefaultString(cashierName)}`}
            </span> */}
            {/* <span className={cellStyle(buyerPhone)}>
              {`买家手机号：${printDefaultString(buyerPhone)}`}
            </span> */}
            <span className={cellStyle(marketingType && !isOfflineBranchStore && !isSingleStore)}>
              {`推广方式：${printDefaultString(marketingTypeTextMap.get(marketingType))}`}
            </span>
            <span className={cellStyle(periodSendTime)}>
              {`周期购送到时间：${printDefaultString(
                printDateBySeconds(periodSendTime, 'YYYY-MM-DD')
              )}`}
            </span>
          </div>
        </div>
        <div className="export-item__operations">
          <div className="export-item__operations-button-group">{renderExportState()}</div>
          {isShowRefresh && (
            <Button onClick={update} loading={isLoading}>
              刷新
            </Button>
          )}
        </div>
      </div>
    </li>
  );
};

export default widthExportState(ExportItem);
