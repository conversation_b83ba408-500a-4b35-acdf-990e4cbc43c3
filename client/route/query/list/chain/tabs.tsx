import * as React from 'react';
import { Tabs } from 'zent';
import styled from 'styled-components';
import { ORDER_TYPE } from 'common/constants/order';
import { OrderSourceType } from 'common/constants/common';
import { orderTypeValue } from 'common/constants/models';
import { orderStateLabel } from 'common/constants/select-text';

const MarginTabs = styled(Tabs)`
  margin-top: 16px;
`;

interface OrderTabsProps {
  orderType?: number;
  orderSource?: number;
  orderStateList?: number[];
  onChange: (value: number[]) => void;
}

export default function OrderTabs(props: OrderTabsProps): React.ReactElement {
  const { orderType = -1, orderSource = -1, onChange, orderStateList = [-1] } = props;

  // 获取基础的订单状态标签
  const baseStateLabels = React.useMemo(() => {
    let tempOrderTypeKey = 'ALL';

    Object.keys(orderTypeValue).some(key => {
      if (orderTypeValue[key] === orderType) {
        tempOrderTypeKey = key;
        return true;
      }
      return false;
    });

    const orderStates = orderStateLabel[tempOrderTypeKey] ?? orderStateLabel.ALL;

    if (
      [OrderSourceType.Eleme, OrderSourceType.Meituan].includes(orderSource) &&
      orderType !== ORDER_TYPE.TUAN.value
    ) {
      return orderStates.concat([{ text: '待接单', value: 50 }]);
    }

    return orderStates;
  }, [orderType, orderSource]);

  // 根据 orderStateList 生成 tabs 和 activeId
  const { stateLabels, activeId } = React.useMemo(() => {
    const baseTabs = baseStateLabels.map(state => ({
      title: state.text,
      key: state.value as string | number
    }));

    // 如果 orderStateList 只有一个元素，activeId 取这个元素
    if (orderStateList.length === 1) {
      return {
        stateLabels: baseTabs,
        activeId: orderStateList[0]
      };
    }

    // 如果 orderStateList 有多个元素，需要添加临时 tab
    if (orderStateList.length > 1) {
      // 过滤掉"全部"选项
      const filteredStateList = orderStateList.filter(state => state !== -1);

      if (filteredStateList.length > 1) {
        // 获取对应的状态名称
        const stateNames = filteredStateList.map(stateValue => {
          const stateItem = baseStateLabels.find(label => label.value === stateValue);
          return stateItem ? stateItem.text : String(stateValue);
        });

        // 创建临时 tab
        const tempTabTitle = stateNames.join('、');
        const tempTabKey = 'multi-select'; // 使用特殊的 key 标识多选状态

        // 在全部 tab 后添加临时 tab
        const newTabs = [...baseTabs];
        const allTabIndex = newTabs.findIndex(tab => tab.key === -1);
        if (allTabIndex !== -1) {
          newTabs.splice(allTabIndex + 1, 0, {
            title: tempTabTitle,
            key: tempTabKey
          });
        } else {
          newTabs.unshift({
            title: tempTabTitle,
            key: tempTabKey
          });
        }

        return {
          stateLabels: newTabs,
          activeId: tempTabKey
        };
      }
    }

    // 默认情况：选择全部或无效状态
    return {
      stateLabels: baseTabs,
      activeId: -1
    };
  }, [baseStateLabels, orderStateList]);

  // 处理 tab 切换
  const handleTabChange = React.useCallback(
    (tabKey: string | number) => {
      if (tabKey === 'multi-select') {
        // 如果点击的是多选 tab，不做任何操作
        return;
      }

      // 如果点击的是具体的状态 tab，更新 orderStateList
      const numericKey = Number(tabKey);
      onChange([numericKey]);
    },
    [onChange]
  );

  return (
    <MarginTabs type="card" activeId={activeId} tabs={stateLabels} onChange={handleTabChange} />
  );
}
