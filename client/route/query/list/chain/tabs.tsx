import * as React from 'react';
import { Tabs } from 'zent';
import styled from 'styled-components';
import { ORDER_TYPE } from 'common/constants/order';
import { OrderSourceType } from 'common/constants/common';
import { orderTypeValue } from 'common/constants/models';
import { orderStateLabel } from 'common/constants/select-text';

const MarginTabs = styled(Tabs)`
  margin-top: 16px;
`;

interface OrderTabsProps {
  orderType?: number;
  orderState?: number;
  orderSource?: number;
  orderStateList?: number[];
  onChange: (value: number) => void;
}

export default function OrderTabs(props: OrderTabsProps): React.ReactElement {
  const {
    orderType = -1,
    orderState = -1,
    orderSource = -1,
    onChange,
    orderStateList = [-1]
  } = props;

  console.log('orderStateList', orderStateList);

  console.log('orderState', orderState);

  const stateLabels = React.useMemo(() => {
    let tempOrderTypeKey = 'ALL';

    Object.keys(orderTypeValue).some(key => {
      if (orderTypeValue[key] === orderType) {
        tempOrderTypeKey = key;
        return true;
      }
      return false;
    });

    const orderStates = orderStateLabel[tempOrderTypeKey] ?? orderStateLabel.ALL;

    if (
      [OrderSourceType.Eleme, OrderSourceType.Meituan].includes(orderSource) &&
      orderType !== ORDER_TYPE.TUAN.value
    ) {
      return orderStates.concat([{ text: '待接单', value: 50 }]).map(state => ({
        title: state.text,
        key: state.value
      }));
    }

    return orderStates.map(state => ({
      title: state.text,
      key: state.value
    }));
  }, [orderType, orderSource]);

  return <MarginTabs type="card" activeId={orderState} tabs={stateLabels} onChange={onChange} />;
}
