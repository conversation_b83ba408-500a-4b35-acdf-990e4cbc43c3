import * as React from 'react';
import { Dialog, Grid } from 'zent';
import { isNil } from 'lodash';
import { Select } from '@zent/compat';
import { formatDatetime, setUrlDomain } from '@youzan/retail-utils';
import { DialogProvider } from '@youzan/retail-components';
import { LinkButton, Divider } from '@youzan/react-components';
import { BatchTaskStatus } from '@youzan/zan-hasaki';
import { ExpressType } from 'common/constants/common';
import * as api from '../api';
import style from './style.scss';

const { useState } = React;

const deliverStatusTextMap = {
  [BatchTaskStatus.Retry]: '已重试',
  [BatchTaskStatus.Failed]: '发货失败',
  [BatchTaskStatus.WaitingToBeProcessed]: '待发货',
  [BatchTaskStatus.Processing]: '待发货',
  [BatchTaskStatus.CompletedOrSucceed]: '发货成功'
};

enum TaskType {
  // 按履约单发货
  Fulfill = 1,
  // 按订单发货
  Order = 2
}

interface IItemData {
  // 任务类型
  taskType?: TaskType;
  // 发货状态
  deliverStatus?: BatchTaskStatus;
  // 发货失败原因/备注
  deliverFailureReason?: string;
  // 发货单号
  fulfillNo?: string;
  // 订单编号
  orderNo?: string;
  // 下单时间
  orderCreateTime?: number;
  // 仓库名称
  warehouseName?: string;
  /** 总任务数量 */
  deliverTotalCount?: number;
  /** 等待任务数量 */
  deliverWaitCount?: number;
  /** 成功任务数量 */
  deliverSuccessCount?: number;
  /** 失败任务数量 */
  deliverFailureCount?: number;
  /** 子任务列表 */
  subTaskList: {}[];
}

interface ITaskDetailDialogProps {
  batchTaskId: string;
  children: ({ onClick }: { onClick: any }) => JSX.Element;
  expressType: ExpressType;
}
interface TaskDataProps {
  subTaskList: any[];
  taskType?: any;
  deliverTotalCount?: any;
  deliverWaitCount?: any;
  deliverSuccessCount?: any;
  deliverFailureCount?: any;
}

function getOrderDetailUrl(
  isTaskFulfill: boolean,
  data: {
    orderNo: string | undefined;
    fulfillNo: string | undefined;
  },
  openFulfillDialog?: boolean
): string {
  const { orderNo, fulfillNo } = data;
  let url;
  if (isTaskFulfill) {
    // 按履约单发货
    url = setUrlDomain(`/v2/order/fulfilldetail#/?fulfillNo=${fulfillNo}`, 'store');
  } else {
    // 按订单发货
    url = setUrlDomain(`/v2/order/orderdetail#/?order_no=${orderNo}`, 'store');
  }
  if (openFulfillDialog) {
    url += '&openFulfillDialog=true';
  }
  return url;
}

enum Status {
  // 全部
  All,
  // 待发货
  Waiting,
  // 发货成功
  Success,
  // 发货失败
  Failed
}

const statusOptions = [
  {
    text: '全部',
    value: Status.All
  },
  {
    text: '待发货',
    value: Status.Waiting
  },
  {
    text: '发货成功',
    value: Status.Success
  },
  {
    text: '发货失败',
    value: Status.Failed
  }
];

export default function TaskDetailDialog({
  batchTaskId,
  children: Children,
  expressType
}: ITaskDetailDialogProps) {
  const [taskData, setTaskData] = useState<TaskDataProps>({ subTaskList: [] });

  function queryBatchtaskDetail() {
    let apiFn = api.queryBatchtaskDetail;
    let params: { batchTaskId: string; expressType?: ExpressType } = { batchTaskId };
    if (expressType === ExpressType.City) {
      apiFn = api.queryBatchtaskDetail;
    } else if (expressType === ExpressType.SelfFetch) {
      apiFn = api.selectDeliveryQueryBatchtaskDetail;
      params = { ...params, expressType };
    }
    return apiFn(params).then(data => {
      setTaskData({
        ...data,
        subTaskList: data.subTaskList || []
      });
    });
  }

  function getColumns(isTaskFulfill: boolean) {
    return [
      {
        title: isTaskFulfill ? '发货单号/订单编号' : '订单编号',
        width: 240,
        bodyRender: (data: IItemData) => {
          if (isTaskFulfill) {
            return (
              <>
                <div>{data.fulfillNo}</div>
                <div className="gray">{data.orderNo}</div>
              </>
            );
          }
          return <div>{data.orderNo}</div>;
        }
      },
      {
        title: '发货方',
        name: 'warehouseName'
      },
      {
        title: '下单时间',
        width: 170,
        bodyRender: (data: IItemData) => {
          return <div>{data.orderCreateTime ? formatDatetime(data.orderCreateTime) : '-'}</div>;
        }
      },
      {
        title: '发货状态',
        bodyRender: (data: IItemData) => {
          const { deliverStatus } = data;
          if (isNil(deliverStatus)) {
            return '-';
          }
          const text = deliverStatusTextMap[deliverStatus];
          if (deliverStatus === BatchTaskStatus.Failed) {
            return <span className="task_error_text">{text}</span>;
          }
          return text;
        }
      },
      {
        title: '备注',
        bodyRender: (data: IItemData) => {
          return <div>{data.deliverFailureReason || '-'}</div>;
        }
      },
      {
        title: '操作',
        width: 100,
        textAlign: 'right',
        bodyRender: (data: IItemData) => {
          const { deliverStatus } = data;
          const items = [
            <LinkButton
              onClick={() => {
                const { orderNo, fulfillNo } = data;
                const url = getOrderDetailUrl(isTaskFulfill, { orderNo, fulfillNo });
                window.open(url);
              }}
            >
              详情
            </LinkButton>
          ];
          if (deliverStatus === BatchTaskStatus.Failed) {
            items.unshift(
              <LinkButton
                onClick={() => {
                  const { orderNo, fulfillNo } = data;
                  const url = getOrderDetailUrl(isTaskFulfill, { orderNo, fulfillNo }, true);
                  window.open(url);
                }}
              >
                发货
              </LinkButton>
            );
          }
          return <Divider items={items} />;
        }
      }
    ];
  }

  const [selectedStatus, setSelectedStatus] = useState(Status.All);

  function onSelectedStatusChange(e: { target: { value: React.SetStateAction<Status> } }) {
    setSelectedStatus(e.target.value);
  }

  const {
    taskType,
    deliverTotalCount,
    deliverWaitCount,
    deliverSuccessCount,
    deliverFailureCount
  } = taskData;
  const isTaskFulfill = taskType === TaskType.Fulfill;

  const subTaskList = taskData?.subTaskList?.filter(v => {
    if (selectedStatus === Status.Waiting) {
      return [BatchTaskStatus.WaitingToBeProcessed, BatchTaskStatus.Processing].includes(
        v.deliverStatus
      );
    }

    if (selectedStatus === Status.Success) {
      return v.deliverStatus === BatchTaskStatus.CompletedOrSucceed;
    }

    if (selectedStatus === Status.Failed) {
      return v.deliverStatus === BatchTaskStatus.Failed;
    }

    return true;
  });

  return (
    <DialogProvider>
      {({ visible, hideDialog, showDialog }) => (
        <>
          <Dialog
            className={style['batch-dialog']}
            title="批量发货任务详情"
            visible={visible}
            onClose={hideDialog}
          >
            <div className={style['batch-dialog-top']}>
              <p className="batch-dialog-top-stat">
                共发货 <b>{deliverTotalCount}</b> 个订单，待发货 <b>{deliverWaitCount || 0}</b> 个，
                发货成功 <b>{deliverSuccessCount}</b> 个
                {!!deliverFailureCount && (
                  <>
                    ，发货失败 <b className="task_error_text">{deliverFailureCount}</b> 个
                  </>
                )}
                。
              </p>
              {expressType === ExpressType.City && (
                <p className="batch-dialog-top-tip">同城配送订单默认以“商家自行配送”发货。</p>
              )}
              {expressType === ExpressType.SelfFetch && (
                <p className="batch-dialog-top-tip">自提订单默认以“无码核销”方式发货。</p>
              )}
              <Select
                className="batch-dialog-top-filter"
                value={selectedStatus}
                data={statusOptions}
                onChange={onSelectedStatusChange}
              />
            </div>
            <Grid columns={getColumns(isTaskFulfill)} datasets={subTaskList} />
          </Dialog>
          <Children
            onClick={() => {
              queryBatchtaskDetail();
              showDialog();
            }}
          />
        </>
      )}
    </DialogProvider>
  );
}
