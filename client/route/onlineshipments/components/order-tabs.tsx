import * as React from 'react';
import { Tabs } from 'zent';
import styled from 'styled-components';

const MarginTabs = styled(Tabs)`
  margin-top: 16px;
`;

interface IQuickSearchItem {
  code: number;
  totalCount: number;
  name: string;
}

interface OrderTabsProps {
  onChange: (value: number) => void;
  quickSearchList: IQuickSearchItem[];
  activeId: number;
}

export default function OrderTabs(props: OrderTabsProps) {
  const { onChange, quickSearchList, activeId } = props;

  console.log('quickSearchList', quickSearchList);

  if (!(quickSearchList && quickSearchList.length > 0)) {
    return null;
  }

  const tabs = [
    {
      key: 0,
      title: '全部'
    },
    ...quickSearchList.map(v => {
      return {
        key: v.code,
        title: `${v.name} ${v.totalCount ? v.totalCount : ''}`
      };
    })
  ];

  return <MarginTabs type="card" activeId={activeId} tabs={tabs} onChange={onChange} />;
}
